import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/notifications/controllers/notification_controller.dart';
import 'package:ivent_app/features/notifications/pages/notifications_page.dart';

abstract class NotificationsRoutes {
  NotificationsRoutes._();

  static const NOTIFICATIONS = '/notifications';
}

class NotificationsBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<NotificationController>()) return;
    Get.lazyPut<NotificationController>(() => NotificationController(Get.find<AuthService>()), fenix: true);
  }
}

List<GetPage<dynamic>> notificationPages = [
  GetPage(
    name: NotificationsRoutes.NOTIFICATIONS,
    page: () => const NotificationsPage(),
    binding: NotificationsBindings(),
  ),
];

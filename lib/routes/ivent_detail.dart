import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/pages/ivent_detail.dart';
import 'package:ivent_app/features/ivent_detail/pages/ivent_detail_collabrators.dart';
import 'package:ivent_app/features/ivent_detail/pages/ivent_detail_invite_more_people.dart';
import 'package:ivent_app/features/ivent_detail/pages/ivent_detail_participants.dart';
import 'package:ivent_app/features/ivent_detail/pages/ivent_detail_squad.dart';
import 'package:ivent_app/features/ivent_detail/pages/ivent_detail_whom_you_join.dart';

abstract class IventDetayRoutes {
  IventDetayRoutes._();

  static const IVENT_DETAY = '/ivent_detay';
  static const IVENT_DETAY_KIMLERLE_KATILIYORSUN = '/ivent_detay_kimlerle_katiliyorsun';
  static const IVENT_DETAY_DAHA_FAZLA_KISI_CAGIR = '/ivent_detay_daha_fazla_kisi_cagir';
  static const IVENT_DETAY_KISILER = '/ivent_detay_kisiler';
  static const IVENT_DETAY_PAYDASLAR = '/ivent_detay_paydaslar';
  static const IVENT_DETAY_SQUAD = '/ivent_detay_squad';
}

class IventDetailBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<IventDetailsController>()) return;
    Get.lazyPut(() => IventDetailStateManager(Get.arguments), tag: Get.arguments, fenix: true);
    Get.lazyPut<IventDetailsController>(
      () => IventDetailsController(
        Get.find<AuthService>(),
        Get.find<IventDetailStateManager>(tag: Get.arguments),
        Get.arguments,
      ),
      tag: Get.arguments,
      fenix: true,
    );
  }
}

List<GetPage<dynamic>> iventDetailPages = [
  GetPage(
    name: IventDetayRoutes.IVENT_DETAY,
    page: () => IventDetail(Get.arguments),
    binding: IventDetailBindings(),
  ),
  GetPage(
    name: IventDetayRoutes.IVENT_DETAY_KIMLERLE_KATILIYORSUN,
    page: () => IventDetailWhomYouJoin(Get.arguments),
  ),
  GetPage(
    name: IventDetayRoutes.IVENT_DETAY_DAHA_FAZLA_KISI_CAGIR,
    page: () => IventDetailInviteMorePeople(Get.arguments),
  ),
  GetPage(
    name: IventDetayRoutes.IVENT_DETAY_KISILER,
    page: () => IventDetailParticipants(Get.arguments),
  ),
  GetPage(
    name: IventDetayRoutes.IVENT_DETAY_PAYDASLAR,
    page: () => IventDetailCollabrators(iventId: Get.arguments),
  ),
  GetPage(
    name: IventDetayRoutes.IVENT_DETAY_SQUAD,
    page: () => IventDetailSquad(Get.arguments),
  ),
];

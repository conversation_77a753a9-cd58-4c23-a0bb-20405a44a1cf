import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/features/ivent_create/pages/ivent_create_date_selection.dart';
import 'package:ivent_app/features/ivent_create/pages/ivent_create_description.dart';
import 'package:ivent_app/features/ivent_create/pages/ivent_create_image_gallery.dart';
import 'package:ivent_app/features/ivent_create/pages/ivent_create_image_selection.dart';
import 'package:ivent_app/features/ivent_create/pages/ivent_create_main_category.dart';
import 'package:ivent_app/features/ivent_create/pages/ivent_create_map.dart';
import 'package:ivent_app/features/ivent_create/pages/ivent_create_preview.dart';
import 'package:ivent_app/features/ivent_create/pages/ivent_create_subcategory.dart';
import 'package:ivent_app/features/ivent_create/pages/ivent_create_tags.dart';
import 'package:ivent_app/features/ivent_create/widgets/panels/ivent_create_privacy_panel.dart';
import 'package:ivent_app/features/ivent_create/widgets/panels/ivent_create_register_type_panel.dart';

abstract class IventCreateRoutes {
  IventCreateRoutes._();

  static const IVENT_OLUSTUR_KATEGORI_SECINIZ = '/ivent_olustur_kategori_seciniz';
  static const IVENT_OLUSTUR_ALT_KATEGORI_SECINIZ = '/ivent_olustur_alt_kategori_seciniz';
  static const IVENT_OLUSTUR_IMAGE_SELECTION = '/ivent_olustur_image_selection';
  static const IVENT_OLUSTUR_IMAGE_GALLERY = '/ivent_olustur_image_gallery';
  static const IVENT_OLUSTUR_DATE_SELECTION = '/ivent_olustur_date_selection';
  static const IVENT_OLUSTUR_MAP = '/ivent_olustur_map';
  static const IVENT_OLUSTUR_PREVIEW = '/ivent_olustur_preview';
  static const IVENT_OLUSTUR_ACIKLAMA = '/ivent_olustur_aciklama';
  static const IVENT_OLUSTUR_TAGS = '/ivent_olustur_tags';
  static const IVENT_OLUSTUR_KAYIT_TURU_SECINIZ = '/ivent_olustur_kayit_turu_seciniz';
  static const IVENT_OLUSTUR_KIMLER_GOREBILIYOR = '/ivent_olustur_kimler_gorebiliyor';
}

class IventCreateBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<IventCreateController>()) return;
    Get.lazyPut(() => IventCreateStateManager(), fenix: true);
    Get.lazyPut<IventCreateController>(
      () => IventCreateController(
        Get.find<AuthService>(),
        Get.find<IventCreateStateManager>(),
      ),
      fenix: true,
    );
  }
}

List<GetPage<dynamic>> iventCreatorPages = [
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_KATEGORI_SECINIZ,
    page: () => const IventCreateMainCategory(),
    binding: IventCreateBindings(),
  ),
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_ALT_KATEGORI_SECINIZ,
    page: () => IventCreateSubcategory(),
  ),
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_IMAGE_SELECTION,
    page: () => IventCreateImageSelection(),
  ),
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_IMAGE_GALLERY,
    page: () => const IventCreateImageGallery(),
  ),
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_DATE_SELECTION,
    page: () => IventCreateDateSelection(),
  ),
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_MAP,
    page: () => const IventCreateMap(),
  ),
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_PREVIEW,
    page: () => IventCreatePreview(),
  ),
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_ACIKLAMA,
    page: () => IventCreateDescription(),
  ),
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_TAGS,
    page: () => const IventCreateTags(),
  ),
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_KAYIT_TURU_SECINIZ,
    page: () => const IventCreateRegisterTypePanel(),
  ),
  GetPage(
    name: IventCreateRoutes.IVENT_OLUSTUR_KIMLER_GOREBILIYOR,
    page: () => const IventCreatePrivacyPanel(),
  ),
];

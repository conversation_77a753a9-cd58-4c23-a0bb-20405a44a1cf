import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/single_vibe_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_page_controller.dart';
import 'package:ivent_app/features/vibes/pages/camera_page.dart';
import 'package:ivent_app/features/vibes/pages/gallery_page.dart';
import 'package:ivent_app/features/vibes/pages/single_vibe_page.dart';
import 'package:ivent_app/features/vibes/pages/vibe_upload.dart';
import 'package:ivent_app/features/vibes/pages/vibes_page.dart';

class VibesRoutes {
  VibesRoutes._();

  static const String SINGLE_VIBE_PAGE = '/single_vibe_page';
  static const String VIBE_UPLOAD_PAGE = '/vibe_upload_page';
  static const String VIBES_PAGE = '/vibes_page';
  static const String CAMERA_PAGE = '/camera_page';
  static const String GALLERY_PAGE = '/gallery_page';
}

class SinglePageVibesBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<SinglePageVibeController>(tag: Get.arguments)) return;
    Get.lazyPut(
      () => SinglePageVibeController(Get.find<AuthService>(), Get.arguments),
      tag: Get.arguments,
      fenix: true,
    );
  }
}

class VibeUploadBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<VibeUploadController>()) return;
    Get.lazyPut(() => VibeUploadController(Get.find<AuthService>()), fenix: true);
  }
}

class VibesPageBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<VibesPageController>()) return;
    Get.lazyPut(() => VibesPageController(Get.find<AuthService>()), fenix: true);
  }
}

List<GetPage<dynamic>> vibesPages = [
  GetPage(
    name: VibesRoutes.SINGLE_VIBE_PAGE,
    page: () => SingleVibePage(vibeId: Get.arguments),
    binding: SinglePageVibesBindings(),
  ),
  GetPage(
    name: VibesRoutes.VIBE_UPLOAD_PAGE,
    page: () => const VibeUpload(),
    binding: VibeUploadBindings(),
  ),
  GetPage(
    name: VibesRoutes.VIBES_PAGE,
    page: () => const VibesPage(),
    binding: VibesPageBindings(),
  ),
  GetPage(
    name: VibesRoutes.CAMERA_PAGE,
    page: () => const CameraPage(),
    binding: VibeUploadBindings(),
  ),
  GetPage(
    name: VibesRoutes.GALLERY_PAGE,
    page: () => const GalleryPage(),
  ),
];

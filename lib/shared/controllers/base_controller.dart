import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/routes/other.dart';

abstract class BaseController extends GetxController {
  final AuthService authService;

  BaseController(this.authService);

  // Shared state
  final _isLoading = false.obs;
  final _errorMessage = ''.obs;

  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;

  SessionUser get sessionUser {
    final user = authService.sessionUser;
    if (user == null) {
      throw Exception('SessionUser is null. User must be logged in to access this controller.');
    }
    return user;
  }

  bool get isLoggedIn => authService.isLoggedIn;

  @mustCallSuper
  void initController() {}

  @mustCallSuper
  void closeController() {}

  @override
  void onInit() {
    super.onInit();
    initController();
  }

  @override
  void onClose() {
    closeController();
    super.onClose();
  }

  void goToSomethingWentWrongPage() => Get.toNamed(OtherRoutes.SOMETHING_WENT_WRONG);

  void clearError() => _errorMessage.value = '';

  void setLoading(bool value) {
    _isLoading.value = value;
    if (value) clearError();
  }

  void setError(String message) {
    _errorMessage.value = message;
    _isLoading.value = false;
  }

  void handleError(dynamic e, StackTrace st, [String? customMessage]) {
    final message = customMessage ?? 'Bir hata oluştu. Lütfen tekrar deneyin.';
    Get.snackbar(
      'Hata',
      message,
      backgroundColor: AppColors.error,
      colorText: AppColors.white,
    );
    debugPrint('Exception: $e\n$st');
    setError(message);
  }

  Future<void> runWithLoading(
    Future<void> Function() action, {
    String? errorMessage,
  }) async {
    try {
      setLoading(true);
      await action();
    } catch (e, st) {
      handleError(e, st, errorMessage);
    } finally {
      setLoading(false);
    }
  }
}

abstract class BaseControllerWithSharedState<T extends GetxController> extends BaseController {
  final T state;

  BaseControllerWithSharedState(AuthService authService, this.state) : super(authService);
}

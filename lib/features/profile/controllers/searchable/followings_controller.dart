import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/routes/profile.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';

class FollowingsController extends BaseControllerWithSharedState<ProfileStateManager> {
  FollowingsController(AuthService authService, ProfileStateManager state) : super(authService, state);

  late final BaseSearchBarController baseSearchBarController;

  final _followingsResult = Rxn<GetFollowingsByUserIdReturn>();

  GetFollowingsByUserIdReturn? get followingsResult => _followingsResult.value;

  TextEditingController get textEditingController => baseSearchBarController.textEditingController;
  String get searchText => baseSearchBarController.text;
  bool get isSearching => baseSearchBarController.isSearching;
  bool get isQueryEmpty => searchText.isEmpty;
  bool get isResultsEmpty => followingsResult?.followings.isEmpty ?? true;

  @override
  Future<void> initController() async {
    super.initController();
    baseSearchBarController =
        Get.put(BaseSearchBarController((q) => _searchFollowings(q: q)), tag: 'FollowingsController');
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>(tag: 'FollowingsController');
    super.closeController();
  }

  Future<void> _searchFollowings({String? q}) async {
    _followingsResult.value = await authService.usersApi.getFollowingsByUserId(
      state.userId,
      q: q,
    );
  }

  Future<void> goToFollowingsPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_TAKIP_ETTIKLERIN, arguments: state.userId);
    if (followingsResult == null) await _searchFollowings();
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/routes/profile.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';

class FollowersController extends BaseControllerWithSharedState<ProfileStateManager> {
  FollowersController(AuthService authService, ProfileStateManager state) : super(authService, state);

  late final BaseSearchBarController baseSearchBarController;

  final _followersResult = Rxn<GetFollowersByUserIdReturn>();

  GetFollowersByUserIdReturn? get followersResult => _followersResult.value;

  TextEditingController get textEditingController => baseSearchBarController.textEditingController;
  String get searchText => baseSearchBarController.text;
  bool get isSearching => baseSearchBarController.isSearching;
  bool get isQueryEmpty => searchText.isEmpty;
  bool get isResultsEmpty => followersResult?.followers.isEmpty ?? true;

  @override
  Future<void> initController() async {
    super.initController();
    baseSearchBarController =
        Get.put(BaseSearchBarController((q) => _searchFollowers(q: q)), tag: 'FollowersController');
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>(tag: 'FollowersController');
    super.closeController();
  }

  Future<void> _searchFollowers({String? q}) async {
    _followersResult.value = await authService.usersApi.getFollowersByUserId(
      state.userId,
      q: q,
    );
  }

  Future<void> goToFollowersPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_TAKIPCILER, arguments: state.userId);
    if (followersResult == null) await _searchFollowers();
  }
}

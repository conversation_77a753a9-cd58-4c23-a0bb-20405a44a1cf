import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/routes/profile.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';

class FriendsController extends BaseControllerWithSharedState<ProfileStateManager> {
  FriendsController(AuthService authService, ProfileStateManager state) : super(authService, state);

  late final BaseSearchBarController baseSearchBarController;

  final _friendsResult = Rxn<SearchFriendsByUserIdReturn>();

  SearchFriendsByUserIdReturn? get friendsResult => _friendsResult.value;

  TextEditingController get textEditingController => baseSearchBarController.textEditingController;
  String get searchText => baseSearchBarController.text;
  bool get isSearching => baseSearchBarController.isSearching;
  bool get isQueryEmpty => searchText.isEmpty;
  bool get isResultsEmpty => friendsResult?.friends.isEmpty ?? true;

  @override
  Future<void> initController() async {
    super.initController();
    baseSearchBarController = Get.put(BaseSearchBarController((q) => _searchFriends(q: q)), tag: 'FriendsController');
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>(tag: 'FriendsController');
    super.closeController();
  }

  Future<void> _searchFriends({String? q}) async {
    _friendsResult.value = await authService.userRelationshipsApi.searchFriendsByUserId(
      state.userId,
      FriendListingTypeEnum.user,
      q: q,
    );
  }

  Future<void> goToFriendsPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_ARKADASLAR, arguments: state.userId);
    if (friendsResult == null) await _searchFriends();
  }
}

import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/routes/profile.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class SocialController extends BaseControllerWithSharedState<ProfileStateManager> {
  SocialController(AuthService authService, ProfileStateManager state) : super(authService, state);

  final _followersContext = Rx<GetFollowersByUserIdReturn?>(null);
  final _followingsContext = Rx<GetFollowingsByUserIdReturn?>(null);
  final _hobbies = Rx<SearchHobbiesReturn?>(null);
  final _groups = RxList<GroupListItem>([]);
  final _groupDetail = Rx<GetGroupByGroupIdReturn?>(null);
  final _groupCreatedName = ''.obs;
  final _groupCreatedThumbnailUrl = ''.obs;
  final _groupCreatedUserIds = RxList<String>([]);

  GetFollowersByUserIdReturn? get followersContext => _followersContext.value;
  GetFollowingsByUserIdReturn? get followingsContext => _followingsContext.value;
  SearchHobbiesReturn? get hobbies => _hobbies.value;
  List<GroupListItem> get groups => _groups;
  GetGroupByGroupIdReturn? get groupDetail => _groupDetail.value;
  String get groupCreatedName => _groupCreatedName.value;
  String get groupCreatedThumbnailUrl => _groupCreatedThumbnailUrl.value;
  List<String> get groupCreatedUserIds => _groupCreatedUserIds;

  set followersContext(GetFollowersByUserIdReturn? value) => _followersContext.value = value;
  set followingsContext(GetFollowingsByUserIdReturn? value) => _followingsContext.value = value;
  set hobbies(SearchHobbiesReturn? value) => _hobbies.value = value;
  set groups(List<GroupListItem> value) => _groups.assignAll(value);
  set groupDetail(GetGroupByGroupIdReturn? value) => _groupDetail.value = value;
  set groupCreatedName(String value) => _groupCreatedName.value = value;
  set groupCreatedThumbnailUrl(String value) => _groupCreatedThumbnailUrl.value = value;
  set groupCreatedUserIds(List<String> value) => _groupCreatedUserIds.assignAll(value);

  Future<void> goToFriendsPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_ARKADASLAR, arguments: state.userId);
    // await getByUserId(token: sessionUser!.token, id: userId);
    // followersContext = getFollowersByUserIdReturn;
  }

  Future<void> goToCreatorFollowerPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_TAKIPCILER, arguments: state.userId);
    followersContext = await authService.usersApi.getFollowersByUserId(state.userId);
  }

  Future<void> goToFollowingsPage() async {
    Get.toNamed(ProfileRoutes.PROFILE_PAGE_TAKIP_ETTIKLERIN, arguments: state.userId);
    followingsContext = await authService.usersApi.getFollowingsByUserId(state.userId);
  }
}

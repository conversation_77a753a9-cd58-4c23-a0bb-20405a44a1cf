import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class EduVerificationController extends BaseController {
  // Form controllers
  final emailController = TextEditingController();
  final verificationCodeController = TextEditingController();
  
  // State variables
  final _isLoading = false.obs;
  final _currentStep = 1.obs; // 1: <PERSON><PERSON>, 2: <PERSON><PERSON>, 3: Üniversite seçimi
  final _hasError = false.obs;
  final _errorMessage = ''.obs;
  final _isCodeSent = false.obs;
  final _isEmailVerified = false.obs;
  
  // University selection
  final _universities = <UniversityItem>[].obs;
  final _selectedUniversity = Rxn<UniversityItem>();
  final _selectedGradStatus = Rxn<UserEduVerificationEnum>();
  final universitySearchController = TextEditingController();
  
  // Timer for code resend
  final _resendTimer = 60.obs;
  
  // Constructor
  EduVerificationController(AuthService authService) : super(authService);
  
  // Getters
  bool get isLoading => _isLoading.value;
  int get currentStep => _currentStep.value;
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;
  bool get isCodeSent => _isCodeSent.value;
  bool get isEmailVerified => _isEmailVerified.value;
  List<UniversityItem> get universities => _universities;
  UniversityItem? get selectedUniversity => _selectedUniversity.value;
  UserEduVerificationEnum? get selectedGradStatus => _selectedGradStatus.value;
  int get resendTimer => _resendTimer.value;
  
  // Form validation
  bool get isEmailValid {
    final email = emailController.text.trim();
    debugPrint('🔍 [EDU] Email validation for: "$email"');
    
    if (email.isEmpty) {
      debugPrint('❌ [EDU] Email is empty');
      return false;
    }
    
    // Basic email format kontrolü
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(email)) {
      debugPrint('❌ [EDU] Email format is invalid');
      return false;
    }
    
    // edu.tr ile biten email kontrolü - tüm edu.tr uzantılı e-postalar geçerli
    final isValid = email.endsWith('.edu.tr');
    debugPrint('${isValid ? '✅' : '❌'} [EDU] Email ends with .edu.tr: $isValid');
    
    return isValid;
  }
  
  bool get isCodeValid => verificationCodeController.text.isNotEmpty && 
                         verificationCodeController.text.length >= 4;
  
  bool get isUniversityStepValid => selectedUniversity != null && 
                                   selectedGradStatus != null;
  
  // Step titles
  String get stepTitle {
    switch (currentStep) {
      case 1:
        return 'edu.tr E-posta Adresinizi Girin';
      case 2:
        return 'Doğrulama Kodu';
      case 3:
        return 'Üniversite Bilgileriniz';
      default:
        return 'edu.tr Doğrulama';
    }
  }
  
  String get stepDescription {
    switch (currentStep) {
      case 1:
        return 'Üniversite e-posta adresinizi (@edu.tr) girin. Size doğrulama kodu göndereceğiz.';
      case 2:
        return 'E-posta adresinize gönderilen doğrulama kodunu girin.';
      case 3:
        return 'Üniversitenizi seçin ve mezuniyet durumunuzu belirtin.';
      default:
        return '';
    }
  }
  
  // Lifecycle
  @override
  void initController() {
    super.initController();
    _loadUniversities();
    debugPrint('EduVerificationController initialized for user: ${sessionUser.sessionId}');
  }
  
  @override
  void dispose() {
    emailController.dispose();
    verificationCodeController.dispose();
    universitySearchController.dispose();
    super.dispose();
  }
  
  // Public methods
  
  /// Sends verification email
  Future<void> sendVerificationEmail() async {
    if (!isEmailValid) {
      _showError('Geçerli bir edu.tr e-posta adresi girin.');
      return;
    }
    
    try {
      setLoading(true);
      
      // İlk önce email'i güncelle
      await authService.usersApi.updateEmailByUserId(
        sessionUser.sessionId,
        UpdateEmailByUserIdDto(newEmail: emailController.text.trim()),
      );
      
      // Sonra doğrulama kodu gönder
      await authService.usersApi.sendVerificationEmail(sessionUser.sessionId);
      
      _isCodeSent.value = true;
      _currentStep.value = 2;
      _startResendTimer();
      
      Get.snackbar(
        'Başarılı',
        'Doğrulama kodu e-posta adresinize gönderildi.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
    } catch (e) {
      _handleApiError(e, 'E-posta gönderilirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }
  
  /// Validates verification code
  Future<void> validateVerificationCode() async {
    if (!isCodeValid) {
      _showError('Geçerli bir doğrulama kodu girin.');
      return;
    }
    
    try {
      setLoading(true);
      
      // API'de kod parametresi yok gibi görünüyor, sadece userId ile doğrulama yapılıyor
      await authService.usersApi.validateEmail(sessionUser.sessionId);
      
      _isEmailVerified.value = true;
      _currentStep.value = 3;
      
      Get.snackbar(
        'Başarılı',
        'E-posta adresiniz doğrulandı.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
    } catch (e) {
      _handleApiError(e, 'Doğrulama kodu hatalı veya süresi dolmuş.');
    } finally {
      setLoading(false);
    }
  }
  
  /// Completes edu verification process
  Future<void> completeEduVerification() async {
    if (!isUniversityStepValid) {
      _showError('Lütfen üniversite ve mezuniyet durumunuzu seçin.');
      return;
    }
    
    try {
      setLoading(true);
      
      // Mezuniyet durumunu güncelle
      await authService.usersApi.updateGradByUserId(
        sessionUser.sessionId,
        UpdateGradByUserIdDto(newGrad: selectedGradStatus!),
      );
      
      // Session'ı güncelle - kullanıcı bilgilerini yeniden yükle
      final updatedUser = await authService.usersApi.getByUserId(sessionUser.sessionId);
      if (updatedUser != null) {
        // Session user'ı güncelleme mantığı burada olacak
        debugPrint('User education verification status updated');
      }
      
      Get.snackbar(
        'Tebrikler!',
        'edu.tr doğrulamanız tamamlandı. Artık üniversite etkinliklerine erişebilirsiniz.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
      
      // Geri dön
      Get.back();
      
    } catch (e) {
      _handleApiError(e, 'Doğrulama tamamlanırken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }
  
  /// Resends verification code
  Future<void> resendVerificationCode() async {
    if (resendTimer > 0) return;
    
    try {
      setLoading(true);
      
      await authService.usersApi.sendVerificationEmail(sessionUser.sessionId);
      
      _startResendTimer();
      
      Get.snackbar(
        'Başarılı',
        'Doğrulama kodu tekrar gönderildi.',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      
    } catch (e) {
      _handleApiError(e, 'Kod tekrar gönderilirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }
  
  /// Searches universities
  Future<void> searchUniversities(String query) async {
    if (query.isEmpty) {
      _universities.assignAll([]);
      return;
    }
    
    try {
      final result = await authService.universitiesApi.searchUniversities(
        q: query,
        limit: 20,
        page: 1,
      );
      
      if (result != null) {
        _universities.assignAll(result.universities);
      }
      
    } catch (e) {
      debugPrint('University search error: $e');
    }
  }
  
  /// Selects university
  void selectUniversity(UniversityItem university) {
    _selectedUniversity.value = university;
  }
  
  /// Selects graduation status
  void selectGradStatus(UserEduVerificationEnum status) {
    _selectedGradStatus.value = status;
  }
  
  /// Goes to previous step
  void goToPreviousStep() {
    if (currentStep > 1) {
      _currentStep.value--;
    }
  }
  
  /// Goes to next step
  void goToNextStep() {
    if (currentStep < 3) {
      _currentStep.value++;
    }
  }
  
  /// Triggers UI update when form fields change
  void triggerUpdate() {
    // This method is called when text fields change to trigger UI reactivity
    // GetX will automatically update the UI when reactive variables change
    debugPrint('🔄 [EDU] UI update triggered - checking form validity');
    debugPrint('  - Email valid: $isEmailValid');
    debugPrint('  - Code valid: $isCodeValid');
    debugPrint('  - University step valid: $isUniversityStepValid');
  }
  
  // Private methods
  
  /// Loads universities
  Future<void> _loadUniversities() async {
    try {
      final result = await authService.universitiesApi.searchUniversities(
        q: '',
        limit: 50,
        page: 1,
      );
      
      if (result != null) {
        _universities.assignAll(result.universities);
      }
      
    } catch (e) {
      debugPrint('Load universities error: $e');
    }
  }
  
  /// Starts resend timer
  void _startResendTimer() {
    _resendTimer.value = 60;
    
    // Timer countdown
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (_resendTimer.value > 0) {
        _resendTimer.value--;
        return true;
      }
      return false;
    });
  }
  
  /// Handles API errors
  void _handleApiError(dynamic error, String defaultMessage) {
    String message = defaultMessage;
    
    if (error is ApiException) {
      switch (error.code) {
        case 400:
          message = 'Geçersiz bilgi girişi.';
          break;
        case 401:
          message = 'Oturum süreniz dolmuş. Lütfen tekrar giriş yapın.';
          break;
        case 404:
          message = 'İstenen kaynak bulunamadı.';
          break;
        case 429:
          message = 'Çok fazla istek gönderildi. Lütfen biraz bekleyin.';
          break;
        case 500:
          message = 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.';
          break;
        default:
          message = error.message ?? defaultMessage;
      }
    }
    
    _showError(message);
    debugPrint('EduVerification API Error: $error');
  }
  
  /// Shows error message
  void _showError(String message) {
    _hasError.value = true;
    _errorMessage.value = message;
    
    Get.snackbar(
      'Hata',
      message,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }
  
  /// Sets loading state
  void setLoading(bool loading) {
    _isLoading.value = loading;
    if (loading) {
      clearError();
    }
  }
  
  /// Clears error state
  void clearError() {
    _hasError.value = false;
    _errorMessage.value = '';
  }
} 
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/content_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/searchable/favorites_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/followers_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/followings_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/friends_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/social_controller.dart';
import 'package:ivent_app/features/profile/controllers/user_info_controller.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Main controller that coordinates all profile feature functionality
///
/// Acts as the central coordinator for user profile management,
/// handling child controllers for user info, content, social interactions,
/// and various searchable lists (iVents, friends, followers, etc.).
class ProfileController extends BaseControllerWithSharedState<ProfileStateManager> {
  // Child controllers
  late final UserInfoController userInfoController;
  late final ContentController contentController;
  late final SocialController socialController;
  late final IventsController iventsController;
  late final FriendsController friendsController;
  late final FollowersController followersController;
  late final FollowingsController followingsController;
  late final FavoritesController favoritesController;

  // Constructor
  ProfileController(AuthService authService, ProfileStateManager state) : super(authService, state);

  // Lifecycle methods

  @override
  void initController() async {
    super.initController();

    // Initialize child controllers with user-specific tags
    final tag = state.userId;
    userInfoController = Get.put(UserInfoController(authService, state), tag: tag);
    contentController = Get.put(ContentController(authService, state), tag: tag);
    socialController = Get.put(SocialController(authService, state), tag: tag);
    iventsController = Get.put(IventsController(authService, state), tag: tag);
    friendsController = Get.put(FriendsController(authService, state), tag: tag);
    followersController = Get.put(FollowersController(authService, state), tag: tag);
    followingsController = Get.put(FollowingsController(authService, state), tag: tag);
    favoritesController = Get.put(FavoritesController(authService, state), tag: tag);

    debugPrint('ProfileController initialized for user: ${sessionUser.sessionId}, profile: ${state.userId}');
  }

  @override
  void closeController() {
    // Clean up child controllers with user-specific tags
    final tag = state.userId;
    Get.delete<UserInfoController>(tag: tag);
    Get.delete<ContentController>(tag: tag);
    Get.delete<SocialController>(tag: tag);
    Get.delete<IventsController>(tag: tag);
    Get.delete<FriendsController>(tag: tag);
    Get.delete<FollowersController>(tag: tag);
    Get.delete<FollowingsController>(tag: tag);
    Get.delete<FavoritesController>(tag: tag);
    super.closeController();
  }

  Future<void> goToIventsPage() async => await iventsController.goToIventsPage();
  Future<void> goToFriendsPage() async => await friendsController.goToFriendsPage();
  Future<void> goToFollowersPage() async => await followersController.goToFollowersPage();
  Future<void> goToFollowingsPage() async => await followingsController.goToFollowingsPage();
  Future<void> goToFavoritesPage() async => await favoritesController.goToFavoritesPage();
}

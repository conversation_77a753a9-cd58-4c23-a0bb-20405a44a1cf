import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/creator_request_controller.dart';

class CreatorRequestPage extends StatefulWidget {
  const CreatorRequestPage({Key? key}) : super(key: key);

  @override
  State<CreatorRequestPage> createState() => _CreatorRequestPageState();
}

class _CreatorRequestPageState extends State<CreatorRequestPage> {
  CreatorRequestController? controller;

  @override
  void initState() {
    super.initState();
    // Create controller manually
    try {
      final authService = Get.find<AuthService>();
      controller = CreatorRequestController(authService);
      Get.put(controller!);
    } catch (e) {
      // If AuthService is not found, navigate back
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.back();
        Get.snackbar(
          'Hata',
          'Oturum açmanız gerekiyor.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      });
    }
  }

  @override
  void dispose() {
    if (controller != null) {
      Get.delete<CreatorRequestController>();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (controller == null) {
      return IaScaffold.noSearch(
        title: 'iVent Creator Başvurusu',
        body: const Center(
          child: CircularProgressIndicator(),
        ),
        showBackButton: true,
      );
    }

    return IaScaffold.noSearch(
      title: 'iVent Creator Başvurusu',
      body: Obx(() {
        if (controller!.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.padding16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              _buildHeaderSection(),
              
              const SizedBox(height: AppDimensions.padding24),
              
              // Status Section
              _buildStatusSection(),
              
              const SizedBox(height: AppDimensions.padding24),
              
              // Benefits Section
              _buildBenefitsSection(),
              
              const SizedBox(height: AppDimensions.padding24),
              
              // Requirements Section (Debug Mode Only)
              if (kDebugMode) ...[
                _buildRequirementsSection(),
                const SizedBox(height: AppDimensions.padding24),
              ],
              
              // Application Section
              _buildApplicationSection(),
              
              const SizedBox(height: AppDimensions.padding32),
            ],
          ),
        );
      }),
      showBackButton: true,
    );
  }
  
  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: const Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: AppDimensions.padding16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'iVent Creator Ol',
                      style: AppTextStyles.size20Bold.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Etkinlik dünyasında öne çık!',
                      style: AppTextStyles.size14Regular.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.padding16),
          Text(
            'iVent Creator programına katılarak etkinlik organizasyonu konusundaki uzmanlığınızı sergileyebilir, daha geniş kitlelere ulaşabilir ve özel avantajlardan yararlanabilirsiniz.',
            style: AppTextStyles.size14Regular.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildStatusSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: controller!.isApplicationSubmitted 
            ? Colors.green.withValues(alpha: 0.1)
            : controller!.canApply 
                ? Colors.blue.withValues(alpha: 0.1)
                : Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: controller!.isApplicationSubmitted 
              ? Colors.green
              : controller!.canApply 
                  ? Colors.blue
                  : Colors.orange,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            controller!.isApplicationSubmitted 
                ? Icons.check_circle
                : controller!.canApply 
                    ? Icons.info
                    : Icons.star,
            color: controller!.isApplicationSubmitted 
                ? Colors.green
                : controller!.canApply 
                    ? Colors.blue
                    : Colors.orange,
          ),
          const SizedBox(width: AppDimensions.padding16),
          Expanded(
            child: Text(
              controller!.applicationStatus,
              style: AppTextStyles.size14Regular.copyWith(
                color: controller!.isApplicationSubmitted 
                    ? Colors.green.shade700
                    : controller!.canApply 
                        ? Colors.blue.shade700
                        : Colors.orange.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildBenefitsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Creator Avantajları',
              style: AppTextStyles.size16Bold,
            ),
            TextButton(
              onPressed: () => controller!.showBenefitsDialog(),
              child: Text(
                'Detayları Gör',
                style: AppTextStyles.size14Regular.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        ...controller!.creatorBenefits.take(4).map((benefit) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  benefit,
                  style: AppTextStyles.size14Regular,
                ),
              ),
            ],
          ),
        )).toList(),
        if (controller!.creatorBenefits.length > 4)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              've ${controller!.creatorBenefits.length - 4} avantaj daha...',
              style: AppTextStyles.size12Regular.copyWith(
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }
  
  Widget _buildRequirementsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Başvuru Şartları (Debug Mode)',
              style: AppTextStyles.size16Bold,
            ),
            TextButton(
              onPressed: () => controller!.showRequirementsDialog(),
              child: Text(
                'Detayları Gör',
                style: AppTextStyles.size14Regular.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        ...controller!.requirementDescriptions.entries.map((entry) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                controller!.levelRequirements[entry.key] == true 
                    ? Icons.check_circle 
                    : Icons.radio_button_unchecked,
                color: controller!.levelRequirements[entry.key] == true 
                    ? Colors.green 
                    : Colors.grey,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  entry.value,
                  style: AppTextStyles.size14Regular.copyWith(
                    color: controller!.levelRequirements[entry.key] == true 
                        ? AppColors.textPrimary 
                        : AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
        )).toList(),
      ],
    );
  }
  

  
  Widget _buildApplicationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Başvuru',
          style: AppTextStyles.size16Bold,
        ),
        const SizedBox(height: AppDimensions.padding16),
        
        if (controller!.canApply) ...[
          Container(
            padding: const EdgeInsets.all(AppDimensions.padding16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Başvuru Süreci',
                  style: AppTextStyles.size14Medium,
                ),
                const SizedBox(height: AppDimensions.padding8),
                Text(
                  '1. Başvuru formunu gönderin\n'
                  '2. Başvurunuz incelenir (1-3 iş günü)\n'
                  '3. Sonuç bildirimini alın\n'
                  '4. Onay durumunda Creator hesabınız aktifleşir',
                  style: AppTextStyles.size14Regular.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppDimensions.padding24),
        ],
        
        // Application Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: controller!.canApply
                ? () => Get.toNamed('/creator_request_step2')
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: controller!.canApply 
                  ? AppColors.primary 
                  : Colors.grey,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: controller!.isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    controller!.isApplicationSubmitted
                        ? 'Başvuru Gönderildi'
                        : controller!.canApply
                            ? 'Devam Et'
                            : 'Başvuru Yapılamaz',
                    style: AppTextStyles.size16Bold.copyWith(
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
        
        if (!controller!.canApply && !controller!.isApplicationSubmitted) ...[
          const SizedBox(height: AppDimensions.padding8),
                      Text(
              'Zaten bir iVent Creator\'sınız veya başvurunuz değerlendiriliyor.',
              style: AppTextStyles.size12Regular.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
        ],
      ],
    );
  }
} 
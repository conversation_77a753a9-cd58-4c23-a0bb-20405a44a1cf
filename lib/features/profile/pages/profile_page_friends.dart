import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_search_placeholder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/friends_controller.dart';
import 'package:ivent_app/routes/profile.dart';

class ProfilePageFriends extends StatelessWidget {
  final String userId;

  const ProfilePageFriends({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileController controller = Get.find(tag: userId);
    final FriendsController friendsController = controller.friendsController;

    return IaScaffold.search(
      title: 'Arkadaşlar',
      textEditingController: friendsController.textEditingController,
      body: Obx(() {
        final friendsResult = friendsController.friendsResult;
        return IaSearchPlaceholder(
          entityName: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
          isLoading: friendsController.isSearching,
          isResultsEmpty: friendsController.isResultsEmpty,
          isQueryEmpty: friendsController.isQueryEmpty,
          initialSearchResultsState: InitialSearchResultsState.LOADED,
          builder: (context) {
            return ListView.separated(
              padding: const EdgeInsets.only(bottom: 100),
              itemCount: friendsResult!.friendCount,
              itemBuilder: (context, index) {
                final friend = friendsResult.friends[index];
                return IaListTile.withImageUrl(
                  avatarUrl: friend.avatarUrl,
                  title: '@${friend.username}',
                  subtitle: friend.university,
                  onTap: () => Get.toNamed(ProfileRoutes.PROFILE_PAGE, arguments: friend.userId),
                  trailing: SharedButtons.moreVertical(),
                );
              },
              separatorBuilder: IaListTile.separatorBuilder20,
            );
          },
        );
      }),
    );
  }
}

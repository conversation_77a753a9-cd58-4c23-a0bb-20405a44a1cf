import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/edu_verification_controller.dart';

class EduVerificationPage extends StatefulWidget {
  const EduVerificationPage({Key? key}) : super(key: key);

  @override
  State<EduVerificationPage> createState() => _EduVerificationPageState();
}

class _EduVerificationPageState extends State<EduVerificationPage> {
  EduVerificationController? controller;

  @override
  void initState() {
    super.initState();
    // Create controller manually
    try {
      final authService = Get.find<AuthService>();
      controller = EduVerificationController(authService);
      Get.put(controller!);
    } catch (e) {
      // If AuthService is not found, navigate back
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.back();
        Get.snackbar(
          'Hata',
          'Oturum açmanız gerekiyor.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      });
    }
  }

  @override
  void dispose() {
    if (controller != null) {
      Get.delete<EduVerificationController>();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (controller == null) {
      return IaScaffold.noSearch(
        title: 'edu.tr Doğrulama',
        body: const Center(child: CircularProgressIndicator()),
        showBackButton: true,
      );
    }

    return IaScaffold.noSearch(
      title: 'edu.tr Doğrulama',
      body: Obx(() {
        if (controller!.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        return _buildBody();
      }),
      showBackButton: true,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress indicator
          _buildProgressIndicator(),
          const SizedBox(height: AppDimensions.padding24),
          
          // Step content
          _buildStepContent(),
          
          const SizedBox(height: AppDimensions.padding32),
          
          // Action buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Obx(() {
      final currentStep = controller!.currentStep;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Adım $currentStep / 3',
            style: AppTextStyles.size14Medium.copyWith(
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: AppDimensions.padding8),
          LinearProgressIndicator(
            value: currentStep / 3,
            backgroundColor: AppColors.grey200,
            valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          const SizedBox(height: AppDimensions.padding16),
          Text(
            controller!.stepTitle,
            style: AppTextStyles.size20Bold,
          ),
          const SizedBox(height: AppDimensions.padding8),
          Text(
            controller!.stepDescription,
            style: AppTextStyles.size14Regular.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      );
    });
  }

  Widget _buildStepContent() {
    return Obx(() {
      switch (controller!.currentStep) {
        case 1:
          return _buildEmailStep();
        case 2:
          return _buildVerificationStep();
        case 3:
          return _buildUniversityStep();
        default:
          return const SizedBox.shrink();
      }
    });
  }

  Widget _buildEmailStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'E-posta Adresi *',
          style: AppTextStyles.size14Medium,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.grey100,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: controller!.isEmailValid 
                  ? AppColors.primary.withOpacity(0.3) 
                  : AppColors.grey300,
            ),
          ),
          child: TextField(
            controller: controller!.emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              hintText: '<EMAIL>',
              hintStyle: AppTextStyles.size16Regular.copyWith(
                color: AppColors.textSecondary,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(AppDimensions.padding16),
              suffixIcon: Icon(
                Icons.email_outlined,
                color: controller!.isEmailValid 
                    ? AppColors.primary 
                    : AppColors.grey500,
              ),
            ),
            style: AppTextStyles.size16Regular,
            onChanged: (value) => controller!.triggerUpdate(),
          ),
        ),
        const SizedBox(height: AppDimensions.padding12),
        Container(
          padding: const EdgeInsets.all(AppDimensions.padding12),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            border: Border.all(
              color: AppColors.primary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.padding8),
              Expanded(
                child: Text(
                  'Sadece .edu.tr uzantılı e-posta adresleri kabul edilir.',
                  style: AppTextStyles.size12Regular.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVerificationStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(AppDimensions.padding16),
          decoration: BoxDecoration(
            color: AppColors.grey50,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(color: AppColors.grey200),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.email,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: AppDimensions.padding12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Kod gönderildi',
                      style: AppTextStyles.size14Medium,
                    ),
                    Text(
                      controller!.emailController.text,
                      style: AppTextStyles.size12Regular.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),
        
        Text(
          'Doğrulama Kodu *',
          style: AppTextStyles.size14Medium,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.grey100,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: controller!.isCodeValid 
                  ? AppColors.primary.withOpacity(0.3) 
                  : AppColors.grey300,
            ),
          ),
          child: TextField(
            controller: controller!.verificationCodeController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: 'Doğrulama kodunu girin',
              hintStyle: AppTextStyles.size16Regular.copyWith(
                color: AppColors.textSecondary,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(AppDimensions.padding16),
              suffixIcon: Icon(
                Icons.security,
                color: controller!.isCodeValid 
                    ? AppColors.primary 
                    : AppColors.grey500,
              ),
            ),
            style: AppTextStyles.size16Regular,
            onChanged: (value) => controller!.triggerUpdate(),
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),
        
        // Resend button
        Obx(() {
          final timer = controller!.resendTimer;
          return GestureDetector(
            onTap: timer > 0 ? null : controller!.resendVerificationCode,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.padding16,
                vertical: AppDimensions.padding8,
              ),
              decoration: BoxDecoration(
                color: timer > 0 ? AppColors.grey200 : AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: timer > 0 ? AppColors.grey300 : AppColors.primary,
                ),
              ),
              child: Text(
                timer > 0 
                    ? 'Tekrar gönder (${timer}s)'
                    : 'Kodu tekrar gönder',
                style: AppTextStyles.size12Medium.copyWith(
                  color: timer > 0 ? AppColors.grey500 : AppColors.primary,
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildUniversityStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // University selection
        Text(
          'Üniversite *',
          style: AppTextStyles.size14Medium,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.grey100,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: controller!.selectedUniversity != null 
                  ? AppColors.primary.withOpacity(0.3) 
                  : AppColors.grey300,
            ),
          ),
          child: TextField(
            controller: controller!.universitySearchController,
            decoration: InputDecoration(
              hintText: 'Üniversite ara...',
              hintStyle: AppTextStyles.size16Regular.copyWith(
                color: AppColors.textSecondary,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(AppDimensions.padding16),
              suffixIcon: const Icon(
                Icons.search,
                color: AppColors.grey500,
              ),
            ),
            style: AppTextStyles.size16Regular,
            onChanged: controller!.searchUniversities,
          ),
        ),
        
        // Selected university display
        Obx(() {
          final selectedUniversity = controller!.selectedUniversity;
          if (selectedUniversity != null) {
            return Container(
              margin: const EdgeInsets.only(top: AppDimensions.padding8),
              padding: const EdgeInsets.all(AppDimensions.padding12),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.school,
                    color: AppColors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: AppDimensions.padding8),
                  Expanded(
                    child: Text(
                      selectedUniversity.universityName,
                      style: AppTextStyles.size14Medium.copyWith(
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () => controller!.selectUniversity(selectedUniversity),
                    child: const Icon(
                      Icons.close,
                      color: AppColors.primary,
                      size: 20,
                    ),
                  ),
                ],
              ),
            );
          }
          return const SizedBox.shrink();
        }),
        
        // University search results
        Obx(() {
          final universities = controller!.universities;
          if (universities.isNotEmpty && controller!.selectedUniversity == null) {
            return Container(
              margin: const EdgeInsets.only(top: AppDimensions.padding8),
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(color: AppColors.grey300),
              ),
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: universities.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final university = universities[index];
                  return ListTile(
                    dense: true,
                    leading: const Icon(
                      Icons.school,
                      color: AppColors.primary,
                      size: 20,
                    ),
                    title: Text(
                      university.universityName,
                      style: AppTextStyles.size14Regular,
                    ),
                    subtitle: university.universityLocationState != null
                        ? Text(
                            university.universityLocationState!,
                            style: AppTextStyles.size12Regular.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          )
                        : null,
                    onTap: () => controller!.selectUniversity(university),
                  );
                },
              ),
            );
          }
          return const SizedBox.shrink();
        }),
        
        const SizedBox(height: AppDimensions.padding16),
        
        // Graduation status selection
        Text(
          'Mezuniyet Durumu *',
          style: AppTextStyles.size14Medium,
        ),
        const SizedBox(height: AppDimensions.padding8),
        
        Obx(() {
          return Column(
            children: UserEduVerificationEnum.values.map((status) {
              final isSelected = controller!.selectedGradStatus == status;
              String title;
              String description;
              
              switch (status) {
                case UserEduVerificationEnum.student:
                  title = 'Öğrenci';
                  description = 'Halen eğitim görüyorum';
                  break;
                case UserEduVerificationEnum.grad:
                  title = 'Mezun';
                  description = 'Mezun oldum';
                  break;
                case UserEduVerificationEnum.unverified:
                default:
                  return const SizedBox.shrink(); // Skip unverified option
              }
              
              return Container(
                margin: const EdgeInsets.only(bottom: AppDimensions.padding8),
                child: GestureDetector(
                  onTap: () => controller!.selectGradStatus(status),
                  child: Container(
                    padding: const EdgeInsets.all(AppDimensions.padding16),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? AppColors.primary.withOpacity(0.1) 
                          : AppColors.grey100,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      border: Border.all(
                        color: isSelected 
                            ? AppColors.primary 
                            : AppColors.grey300,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isSelected 
                                ? AppColors.primary 
                                : AppColors.white,
                            border: Border.all(
                              color: isSelected 
                                  ? AppColors.primary 
                                  : AppColors.grey400,
                              width: 2,
                            ),
                          ),
                          child: isSelected
                              ? const Icon(
                                  Icons.check,
                                  size: 12,
                                  color: AppColors.white,
                                )
                              : null,
                        ),
                        const SizedBox(width: AppDimensions.padding12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                title,
                                style: AppTextStyles.size14Medium.copyWith(
                                  color: isSelected 
                                      ? AppColors.primary 
                                      : AppColors.textPrimary,
                                ),
                              ),
                              Text(
                                description,
                                style: AppTextStyles.size12Regular.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          );
        }),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Obx(() {
      final currentStep = controller!.currentStep;
      
      return Column(
        children: [
          // Main action button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _getMainButtonAction(),
              style: ElevatedButton.styleFrom(
                backgroundColor: _isMainButtonEnabled() 
                    ? AppColors.primary 
                    : AppColors.grey300,
                padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
              child: controller!.isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                      ),
                    )
                  : Text(
                      _getMainButtonText(),
                      style: AppTextStyles.size16Bold.copyWith(
                        color: AppColors.white,
                      ),
                    ),
            ),
          ),
          
          // Back button (only for steps 2 and 3)
          if (currentStep > 1) ...[
            const SizedBox(height: AppDimensions.padding12),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: controller!.goToPreviousStep,
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
                ),
                child: Text(
                  'Geri',
                  style: AppTextStyles.size16Medium.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ),
            ),
          ],
        ],
      );
    });
  }

  VoidCallback? _getMainButtonAction() {
    if (!_isMainButtonEnabled() || controller!.isLoading) return null;
    
    switch (controller!.currentStep) {
      case 1:
        return controller!.sendVerificationEmail;
      case 2:
        return controller!.validateVerificationCode;
      case 3:
        return controller!.completeEduVerification;
      default:
        return null;
    }
  }

  bool _isMainButtonEnabled() {
    switch (controller!.currentStep) {
      case 1:
        return controller!.isEmailValid;
      case 2:
        return controller!.isCodeValid;
      case 3:
        return controller!.isUniversityStepValid;
      default:
        return false;
    }
  }

  String _getMainButtonText() {
    switch (controller!.currentStep) {
      case 1:
        return 'Doğrulama Kodu Gönder';
      case 2:
        return 'Kodu Doğrula';
      case 3:
        return 'Doğrulamayı Tamamla';
      default:
        return 'Devam Et';
    }
  }
} 
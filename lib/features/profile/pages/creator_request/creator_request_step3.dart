import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/widgets/layout/scaffolds/ia_scaffold.dart';
import '../../controllers/creator_request_controller.dart';

class CreatorRequestStep3 extends StatefulWidget {
  const CreatorRequestStep3({super.key});

  @override
  State<CreatorRequestStep3> createState() => _CreatorRequestStep3State();
}

class _CreatorRequestStep3State extends State<CreatorRequestStep3> {
  final _formKey = GlobalKey<FormState>();
  late CreatorRequestController controller;
  
  // Social media controllers
  final _instagramController = TextEditingController();
  final _facebookController = TextEditingController();
  final _twitterController = TextEditingController();
  
  // Checkbox states
  bool _vibesShareEnabled = true;
  bool _socialMediaBioEnabled = false;
  
  // City dropdown
  String? _selectedCity;
  final List<String> _cities = [
    'Adana', 'Adıyaman', 'Afyonkarahisar', 'Ağrı', 'Amasya', 'Ankara', 'Antalya',
    'Artvin', 'Aydın', 'Balıkesir', 'Bilecik', 'Bingöl', 'Bitlis', 'Bolu',
    'Burdur', 'Bursa', 'Çanakkale', 'Çankırı', 'Çorum', 'Denizli', 'Diyarbakır',
    'Edirne', 'Elazığ', 'Erzincan', 'Erzurum', 'Eskişehir', 'Gaziantep', 'Giresun',
    'Gümüşhane', 'Hakkâri', 'Hatay', 'Isparta', 'Mersin', 'İstanbul', 'İzmir',
    'Kars', 'Kastamonu', 'Kayseri', 'Kırklareli', 'Kırşehir', 'Kocaeli', 'Konya',
    'Kütahya', 'Malatya', 'Manisa', 'Kahramanmaraş', 'Mardin', 'Muğla', 'Muş',
    'Nevşehir', 'Niğde', 'Ordu', 'Rize', 'Sakarya', 'Samsun', 'Siirt', 'Sinop',
    'Sivas', 'Tekirdağ', 'Tokat', 'Trabzon', 'Tunceli', 'Şanlıurfa', 'Uşak',
    'Van', 'Yozgat', 'Zonguldak', 'Aksaray', 'Bayburt', 'Karaman', 'Kırıkkale',
    'Batman', 'Şırnak', 'Bartın', 'Ardahan', 'Iğdır', 'Yalova', 'Karabük', 'Kilis',
    'Osmaniye', 'Düzce'
  ];

  @override
  void initState() {
    super.initState();
    controller = Get.find<CreatorRequestController>();
    _loadExistingData();
  }

  void _loadExistingData() {
    // Pre-fill with existing user data
    controller.emailController.text = controller.email.isNotEmpty 
        ? controller.email 
        : '';
    
    if (controller.city.isNotEmpty) {
      _selectedCity = controller.city;
    }
    
    _instagramController.text = controller.instagramHandle;
    _twitterController.text = controller.twitterHandle;
  }

  @override
  void dispose() {
    _instagramController.dispose();
    _facebookController.dispose();
    _twitterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: 'iVent Creator',
      showBackButton: true,
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress indicator
              LinearProgressIndicator(
                value: controller.progressPercentage,
                backgroundColor: AppColors.grey300,
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              const SizedBox(height: 20),
              
              // Step indicator
              Text(
                '3/3 Adım',
                style: AppTextStyles.size12Regular.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 30),
              
              // Email field
              _buildTextField(
                controller: controller.emailController,
                label: 'E-Mail Adresi',
                isRequired: true,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'E-posta adresi zorunludur';
                  }
                  if (!GetUtils.isEmail(value)) {
                    return 'Geçerli bir e-posta adresi girin';
                  }
                  return null;
                },
                onChanged: (value) => controller.email = value,
              ),
              
              const SizedBox(height: 20),
              
              // City dropdown
              _buildCityDropdown(),
              
              const SizedBox(height: 30),
              
              // Social media section
              _buildSocialMediaSection(),
              
              const SizedBox(height: 40),
              
              // Checkboxes
              _buildCheckboxSection(),
              
              const SizedBox(height: 40),
              
              // Submit button
              _buildSubmitButton(),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    bool isRequired = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    required Function(String) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.grey100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        validator: validator,
        onChanged: onChanged,
        style: AppTextStyles.size16Regular.copyWith(
          color: AppColors.textPrimary,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildCityDropdown() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.grey100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedCity,
        decoration: InputDecoration(
          labelText: 'Yaşadığın Şehir',
          labelStyle: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        items: _cities.map((String city) {
          return DropdownMenuItem<String>(
            value: city,
            child: Text(
              city,
              style: AppTextStyles.size16Regular.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          );
        }).toList(),
        onChanged: (String? newValue) {
          setState(() {
            _selectedCity = newValue;
            controller.city = newValue ?? '';
          });
        },
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Şehir seçimi zorunludur';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildSocialMediaSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Add social media button
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: AppColors.grey100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.add,
                color: AppColors.textSecondary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Sosyal Medya Bağlantısı Ekle',
                style: AppTextStyles.size16Regular.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 20),
        
        // Instagram
        _buildSocialMediaField(
          controller: _instagramController,
          placeholder: 'instagram.com/kullaniciadi',
          onChanged: (value) => controller.instagramHandle = value,
        ),
        
        const SizedBox(height: 16),
        
        // Facebook
        _buildSocialMediaField(
          controller: _facebookController,
          placeholder: 'facebook.com/kullaniciadi',
          onChanged: (value) {
            // Facebook için controller'da alan yok, gerekirse eklenebilir
          },
        ),
        
        const SizedBox(height: 16),
        
        // Twitter
        _buildSocialMediaField(
          controller: _twitterController,
          placeholder: 'twitter.com/kullaniciadi',
          onChanged: (value) => controller.twitterHandle = value,
        ),
      ],
    );
  }

  Widget _buildSocialMediaField({
    required TextEditingController controller,
    required String placeholder,
    required Function(String) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.grey100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        controller: controller,
        onChanged: onChanged,
        style: AppTextStyles.size16Regular.copyWith(
          color: AppColors.textPrimary,
        ),
        decoration: InputDecoration(
          hintText: placeholder,
          hintStyle: AppTextStyles.size16Regular.copyWith(
            color: AppColors.textSecondary,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildCheckboxSection() {
    return Column(
      children: [
        // First checkbox
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Checkbox(
              value: _vibesShareEnabled,
              onChanged: (bool? value) {
                setState(() {
                  _vibesShareEnabled = value ?? false;
                });
              },
              activeColor: AppColors.primary,
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _vibesShareEnabled = !_vibesShareEnabled;
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Text(
                    'Katıldığım etkinliklerde Vibes paylaşabilirim.',
                    style: AppTextStyles.size14Regular.copyWith(
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 10),
        
        // Second checkbox
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Checkbox(
              value: _socialMediaBioEnabled,
              onChanged: (bool? value) {
                setState(() {
                  _socialMediaBioEnabled = value ?? false;
                });
              },
              activeColor: AppColors.primary,
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _socialMediaBioEnabled = !_socialMediaBioEnabled;
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Text(
                    'Sosyal medya profilime "iVent Creator" yazısı ve iVent app profil linki koyabilirim.',
                    style: AppTextStyles.size14Regular.copyWith(
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: Obx(() => ElevatedButton(
        onPressed: controller.isLoading ? null : _submitForm,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: controller.isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                'iVent Creator Başvurusu Gönder',
                style: AppTextStyles.size16Bold.copyWith(
                  color: Colors.white,
                ),
              ),
      )),
    );
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      // Validate required fields
      if (_selectedCity == null || _selectedCity!.isEmpty) {
        Get.snackbar(
          'Hata',
          'Lütfen şehir seçimi yapın.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }
      
      // Save checkbox states to controller if needed
      // controller.vibesShareEnabled = _vibesShareEnabled;
      // controller.socialMediaBioEnabled = _socialMediaBioEnabled;
      
      await controller.sendCreatorRequest();
    }
  }
} 
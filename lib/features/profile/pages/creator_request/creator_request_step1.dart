import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/profile/controllers/creator_request_controller.dart';

class CreatorRequestStep1 extends StatefulWidget {
  const CreatorRequestStep1({Key? key}) : super(key: key);

  @override
  State<CreatorRequestStep1> createState() => _CreatorRequestStep1State();
}

class _CreatorRequestStep1State extends State<CreatorRequestStep1> {
  CreatorRequestController? controller;

  @override
  void initState() {
    super.initState();
    // Create controller manually
    try {
      final authService = Get.find<AuthService>();
      controller = CreatorRequestController(authService);
      Get.put(controller!);
    } catch (e) {
      // If AuthService is not found, navigate back
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.back();
        Get.snackbar(
          'Hata',
          'Oturum açmanız gerekiyor.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      });
    }
  }

  @override
  void dispose() {
    if (controller != null) {
      Get.delete<CreatorRequestController>();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (controller == null) {
      return IaScaffold.noSearch(
        title: 'iVent Creator',
        body: const Center(
          child: CircularProgressIndicator(),
        ),
        showBackButton: true,
      );
    }

    return IaScaffold.noSearch(
      title: 'iVent Creator',
      body: Obx(() {
        if (controller!.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Requirements Section
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Tam Hesap olmak
                    _buildRequirementItem(
                      isCompleted: controller!.isCompleteAccount,
                      title: 'Tam Hesap olmak',
                    ),
                    const SizedBox(height: 20),
                    
                    // Bilinçli Hesap olmak
                    _buildRequirementItem(
                      isCompleted: controller!.isConsciousAccount,
                      title: 'Bilinçli Hesap olmak',
                    ),
                    const SizedBox(height: 20),
                    
                    // 10 Arkadaşını davet et
                    _buildRequirementItem(
                      isCompleted: controller!.hasInvitedFriends,
                      title: '10 Arkadaşını davet et. (${controller!.invitedFriendsCount}/10)',
                      isOrange: true,
                    ),
                  ],
                ),
              ),
              
              // Action Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: controller!.allRequirementsMet ? _onNextPressed : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: controller!.allRequirementsMet 
                        ? AppColors.primary 
                        : AppColors.grey400,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    controller!.allRequirementsMet 
                        ? 'Devam Et' 
                        : 'Devam Et',
                    style: AppTextStyles.size16Bold.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
      showBackButton: true,
    );
  }

  Widget _buildRequirementItem({
    required bool isCompleted,
    required String title,
    bool isOrange = false,
  }) {
    return Row(
      children: [
        // Checkbox icon
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isCompleted ? AppColors.primary : Colors.transparent,
            border: Border.all(
              color: isCompleted ? AppColors.primary : AppColors.grey400,
              width: 2,
            ),
          ),
          child: isCompleted
              ? const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                )
              : null,
        ),
        const SizedBox(width: 16),
        
        // Title
        Expanded(
          child: Text(
            title,
            style: AppTextStyles.size16Regular.copyWith(
              color: isOrange ? AppColors.orange : AppColors.textPrimary,
              fontWeight: isCompleted ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }

  void _onNextPressed() {
    if (controller!.allRequirementsMet) {
      controller!.goToStep2();
    }
  }
} 
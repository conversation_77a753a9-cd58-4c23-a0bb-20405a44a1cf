import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_text_styles.dart';
import '../../../../core/widgets/layout/scaffolds/ia_scaffold.dart';
import '../../controllers/creator_request_controller.dart';

class CreatorRequestStep2 extends StatelessWidget {
  const CreatorRequestStep2({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CreatorRequestController>();
    
    return IaScaffold.noSearch(
      title: 'iVent Creator',
      showBackButton: true,
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Progress indicator
            LinearProgressIndicator(
              value: controller.progressPercentage,
              backgroundColor: AppColors.grey300,
              valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            const SizedBox(height: 20),
            
            // Step indicator
            Text(
              '2/3 Adım',
              style: AppTextStyles.size12Regular.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 10),
            
            // Title
            Text(
              'Açıklama Yazınız',
              style: AppTextStyles.size20Bold.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 20),
            
            // Description
            Text(
              'Neden iVent Creator olmak istiyorsun? Ne tarzda etkinlikler organize etmekten hoşlanırsın? Kısa bir yazıyla iVent App ile nasıl bir işbirliği içerisinde olabileceğine dair bize kısa bir başvuru yazısı yollayabilirsin. Unutma minimum karakter sayısı 140 olacak. Daha kısa bir yazı yazarsan gönderemezsin.',
              style: AppTextStyles.size14Regular.copyWith(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 30),
            
            // Text field
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.grey100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.grey300,
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: controller.descriptionController,
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  style: AppTextStyles.size14Regular.copyWith(
                    color: AppColors.textPrimary,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Açıklama Yazınız',
                    hintStyle: AppTextStyles.size14Regular.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(16),
                  ),
                  onChanged: (value) {
                    controller.description.value = value;
                  },
                ),
              ),
            ),
            const SizedBox(height: 20),
            
            // Character count
            Obx(() => Text(
              '${controller.description.value.length}/140',
              style: AppTextStyles.size12Regular.copyWith(
                color: controller.description.value.length >= 140 
                    ? AppColors.success 
                    : AppColors.textSecondary,
              ),
            )),
            const SizedBox(height: 20),
            
            // Continue button
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton(
                onPressed: controller.description.value.length >= 140
                    ? () => controller.goToStep3()
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: controller.description.value.length >= 140
                      ? AppColors.primary
                      : AppColors.grey400,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  controller.description.value.length >= 140
                      ? 'Devam Et'
                      : 'Minimum 140 karakter yazın',
                  style: AppTextStyles.size16Bold.copyWith(
                    color: Colors.white,
                  ),
                ),
              )),
            ),
          ],
        ),
      ),
    );
  }
} 
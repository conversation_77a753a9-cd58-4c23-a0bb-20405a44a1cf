import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_controller.dart';
import 'package:ivent_app/features/auth/widgets/common/auth_info_text_widget.dart';
import 'package:ivent_app/features/auth/widgets/form/phone_input_widget.dart';

class PhonePage extends StatefulWidget {
  const PhonePage({super.key});

  @override
  State<PhonePage> createState() => _PhonePageState();
}

class _PhonePageState extends State<PhonePage> {
  late final AuthController _controller;
  late final TextEditingController _textFieldController;

  bool _canContinueToNextPage = false;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<AuthController>();
    _textFieldController = TextEditingController();

    if (_controller.state.phoneNumber.isNotEmpty) {
      _textFieldController.text = _controller.state.phoneNumber;
      _canContinueToNextPage = _controller.state.isPhoneNumberValid;
    }
  }

  @override
  void dispose() {
    _textFieldController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.auth(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          const AuthInfoTextWidget(text: AuthStrings.iletisimNumGirerek),
          const SizedBox(height: AuthDimensions.sectionSpacing),
          PhoneInputWidget(
            controller: _textFieldController,
            onValidationChanged: _handleValidationChanged,
            onChanged: _handlePhoneNumberChanged,
          ),
          const Spacer(),
          IaFloatingActionButton(
            isEnabled: _canContinueToNextPage,
            text: AuthStrings.devamEt,
            onPressed: _handleContinuePressed,
          ),
        ],
      ),
    );
  }

  void _handleValidationChanged(bool isValid) {
    _canContinueToNextPage = isValid;
    setState(() {});
  }

  void _handlePhoneNumberChanged(String phoneNumber) {
    _controller.state.phoneNumber = phoneNumber;
  }

  void _handleContinuePressed() {
    if (_canContinueToNextPage) _controller.goToValidatePhonePage();
  }
}

import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';

class AuthStateManager extends GetxController {
  final _phoneNumber = ''.obs;
  String get phoneNumber => _phoneNumber.value;
  set phoneNumber(String value) => _phoneNumber.value = value;

  // Contacts
  final _pendingContactIds = <String>[].obs;
  final _getContactsReturn = Rxn<GetContactsByUserIdReturn>();
  List<String> get pendingContactIds => _pendingContactIds;
  GetContactsByUserIdReturn? get getContactsReturn => _getContactsReturn.value;
  set pendingContactIds(List<String> value) => _pendingContactIds.assignAll(value);
  set getContactsReturn(GetContactsByUserIdReturn? value) => _getContactsReturn.value = value;

  // Registration
  final _fullname = ''.obs;
  final _checkedHobbyIds = <String>[].obs;
  final _registerReturn = Rxn<RegisterReturn>();
  String get fullname => _fullname.value;
  List<String> get checkedHobbyIds => _checkedHobbyIds;
  RegisterReturn? get registerReturn => _registerReturn.value;
  set fullname(String value) => _fullname.value = value.trim();
  set checkedHobbyIds(List<String> value) => _checkedHobbyIds.assignAll(value);
  set registerReturn(RegisterReturn? value) => _registerReturn.value = value;

  // Validation
  final _validationCode = ''.obs;
  final _validateReturn = Rxn<ValidateReturn>();
  String get validationCode => _validationCode.value;
  ValidateReturn? get validateReturn => _validateReturn.value;
  set validationCode(String value) => _validationCode.value = value;
  set validateReturn(ValidateReturn? value) => _validateReturn.value = value;

  String get formattedPhoneNumber {
    if (phoneNumber.length != AuthValidationConstants.phoneNumberMaxLength) {
      return '';
    }
    return _formatPhoneNumber(phoneNumber);
  }

  bool get isPhoneNumberValid => phoneNumber.length == AuthValidationConstants.phoneNumberMaxLength;

  String _formatPhoneNumber(String value) {
    if (value.length != AuthValidationConstants.phoneNumberMaxLength) {
      return value;
    }

    final String areaCode = value.substring(0, 3);
    final String remainingNumber = value.substring(3);
    return '${AuthValidationConstants.turkeyCountryCode}($areaCode)$remainingNumber';
  }
}

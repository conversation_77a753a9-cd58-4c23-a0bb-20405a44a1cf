import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/routes/auth.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/dialogs/ia_alert_dialog.dart';
import 'package:ivent_app/features/ivent_create/controllers/base_ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class IventCreateFormController extends BaseIventCreateController {
  IventCreateFormController(AuthService authService, IventCreateStateManager state) : super(authService, state);

  var _selectedCategory = Rxn<Hobby>();
  var _selectedSubCategory = Rxn<Hobby>();
  var _iventName = ''.obs;
  var _description = Rxn<String>();
  var _googleFormsUrl = Rxn<String>();
  var _instagramUsername = Rxn<String>();
  var _whatsappUrl = Rxn<String>();
  var _isWhatsappUrlPrivate = false.obs;
  var _whatsappNumber = Rxn<String>();
  var _callNumber = Rxn<String>();
  var _websiteUrl = Rxn<String>();
  var _selectedTags = <Hobby>[].obs;
  var _selectedIventPrivacy = IventPrivacyEnum.public.obs;
  var _dates = <DateTime>[].obs;

  Hobby? get selectedCategory => _selectedCategory.value;
  Hobby? get selectedSubCategory => _selectedSubCategory.value;
  String get iventName => _iventName.value;
  String? get description => _description.value;
  String? get googleFormsUrl => _googleFormsUrl.value;
  String? get instagramUsername => _instagramUsername.value;
  String? get whatsappUrl => _whatsappUrl.value;
  bool get isWhatsappUrlPrivate => _isWhatsappUrlPrivate.value;
  String? get whatsappNumber => _whatsappNumber.value;
  String? get callNumber => _callNumber.value;
  String? get websiteUrl => _websiteUrl.value;
  List<Hobby> get selectedTags => _selectedTags;
  IventPrivacyEnum get selectedIventPrivacy => _selectedIventPrivacy.value;
  List<DateTime> get dates => _dates;

  set selectedCategory(Hobby? value) => _selectedCategory.value = value;
  set selectedSubCategory(Hobby? value) => _selectedSubCategory.value = value;
  set iventName(String value) => _iventName.value = value;
  set description(String? value) => _description.value = value;
  set googleFormsUrl(String? value) => _googleFormsUrl.value = value;
  set instagramUsername(String? value) => _instagramUsername.value = value;
  set whatsappUrl(String? value) => _whatsappUrl.value = value;
  set isWhatsappUrlPrivate(bool value) => _isWhatsappUrlPrivate.value = value;
  set whatsappNumber(String? value) => _whatsappNumber.value = value;
  set callNumber(String? value) => _callNumber.value = value;
  set websiteUrl(String? value) => _websiteUrl.value = value;
  set selectedTags(List<Hobby> value) => _selectedTags.assignAll(value);
  set selectedIventPrivacy(IventPrivacyEnum value) => _selectedIventPrivacy.value = value;
  set dates(List<DateTime> value) => _dates.assignAll(value);

  bool get isThereAnyRegisterType =>
      (googleFormsUrl != null && googleFormsUrl!.isNotEmpty) ||
      (instagramUsername != null && instagramUsername!.isNotEmpty) ||
      (whatsappUrl != null && whatsappUrl!.isNotEmpty) ||
      (whatsappNumber != null && whatsappNumber!.isNotEmpty) ||
      (callNumber != null && callNumber!.isNotEmpty) ||
      (websiteUrl != null && websiteUrl!.isNotEmpty);

  List<String> get selectedTagIds => _selectedTags.map((var element) => element.hobbyId).toList();
  List<String> get selectedTagNames => _selectedTags.map((var element) => element.hobbyName).toList();

  @override
  void initController() async {
    super.initController();
    print('IventCreateFormController has been initialized');
  }

  void toggleTag(Hobby tag) {
    if (selectedTagIds.contains(tag.hobbyId)) {
      selectedTags.removeWhere((element) => element.hobbyId == tag.hobbyId);
    } else {
      selectedTags.add(tag);
    }
  }

  Future<void> submitIvent(BuildContext context) async {
    if (state.selectedPlace == null) return;

    final selectedPlace = state.selectedPlace!;
    final latitude = selectedPlace.latitude;
    final longitude = selectedPlace.longitude;
    final mapboxId = selectedPlace.mapboxId;

    final categoryTagId = selectedSubCategory!.hobbyId;
    final privacy = selectedIventPrivacy;

    final dto = CreateIventDto(
      creatorType: AccountTypeEnum.user,
      iventName: iventName,
      thumbnailBuffer: state.selectedImageFile != null ? base64Encode(state.selectedImageFile!) : null,
      dates: dates.map((var date) => date.toIso8601String()).toList(),
      latitude: latitude,
      longitude: longitude,
      categoryTagId: categoryTagId,
      privacy: privacy,
      tagIds: selectedTagIds,
      allowedUniversityCodes: [],
      collabs: [],
      description: description,
      googleFormsUrl: googleFormsUrl,
      instagramUsername: instagramUsername,
      whatsappUrl: whatsappUrl,
      whatsappNumber: whatsappNumber,
      callNumber: callNumber,
      websiteUrl: websiteUrl,
      mapboxId: mapboxId!,
    );
    print(dto.toJson());

    authService.iventsApi.createIvent(dto);

    Get.toNamed(AuthRoutes.APP_NAVIGATION);
    Future.microtask(
      () => showDialog(
        context: context,
        builder: (context) => IaAlertDialog.iventCreate(
          iventName: iventName,
          onOk: () => Get.back(),
          onCancel: () => Get.back(),
        ),
      ),
    );
  }
}

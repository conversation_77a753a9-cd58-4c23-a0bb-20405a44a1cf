import 'dart:async';

import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/base_ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/features/mapbox/controllers/mapbox_controller.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';
import 'package:ivent_app/features/mapbox/models/marker_feature.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

/// Controller for managing map functionality in iVent creation
///
/// Handles map interactions, location search using Mapbox 'suggest' endpoint,
/// place selection, and marker management for the iVent creation process.
/// Integrates with MapboxController for core map functionality and provides
/// search capabilities for location selection.
class IventCreateMapController extends BaseIventCreateController {
  // Controllers
  late final BaseSearchBarController baseSearchBarController;
  late final MapboxController mapboxController;

  // Reactive state
  final _markers = <PointAnnotationOptions>[].obs;
  final _panelController = PanelController();
  final _searchSuggestions = <SearchBoxSuggestFeature>[].obs;

  // Constructor
  IventCreateMapController(AuthService authService, IventCreateStateManager state) : super(authService, state) {
    mapboxController = MapboxController(authService: authService);
  }

  // Getters
  List<PointAnnotationOptions> get markers => _markers;
  PanelController get panelController => _panelController;
  List<SearchBoxSuggestFeature> get searchSuggestions => _searchSuggestions;

  // Setters
  set markers(List<PointAnnotationOptions> value) => _markers.assignAll(value);

  // Lifecycle methods

  @override
  void initController() async {
    super.initController();
    baseSearchBarController = Get.put(BaseSearchBarController(_searchPlaces));
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>();
    super.closeController();
  }

  // Public methods

  /// Selects a place from search suggestions and updates map
  Future<void> selectPlace(SearchBoxSuggestFeature suggestion) async {
    try {
      setLoading(true);

      final result = await authService.mapboxApi.searchBoxRetrieve(
        suggestion.mapboxId,
        sessionUser.sessionId,
      );

      if (result != null && result.features.isNotEmpty) {
        await _updateSelectedPlace(result.features[0], suggestion.name);
      }

      setLoading(false);
    } catch (e) {
      handleError(e, 'Konum seçilirken bir hata oluştu.');
    }
  }

  /// Clears search results and selected place
  void clearSearch() {
    baseSearchBarController.clearSearch();
    searchSuggestions.clear();
    state.selectedPlace = null;
    markers = [];
    _removeSelectedPlaceMarker();
  }

  // Private methods

  /// Searches for places using Mapbox suggest endpoint
  Future<void> _searchPlaces(String? query) async {
    if (query == null || query.isEmpty) {
      searchSuggestions.clear();
      return;
    }

    try {
      final result = await authService.mapboxApi.searchBoxSuggest(
        query,
        sessionUser.sessionId,
        limit: 10,
        proximity: mapboxController.userLocation != null
            ? '${mapboxController.userLocationCoordinates!.lng},${mapboxController.userLocationCoordinates!.lat}'
            : null,
        types: 'country,region,postcode,district,place,city,locality,neighborhood,street,address,poi,category',
      );

      if (result != null) {
        searchSuggestions.assignAll(result.suggestions);
      }
    } catch (e) {
      handleError(e, 'Konum aranırken bir hata oluştu.');
    }
  }

  /// Updates the selected place and map display
  Future<void> _updateSelectedPlace(SearchBoxFeature feature, String placeName) async {
    state.selectedPlace = IaLocationItem.fromProperties(feature.properties);
    panelController.close();
    baseSearchBarController.text = placeName;
    searchSuggestions.clear();

    // Update map marker
    _removeSelectedPlaceMarker();
    _addSelectedPlaceMarker();

    // Fly to selected location
    await _flyToSelectedPlace();
  }

  /// Removes the selected place marker from map
  void _removeSelectedPlaceMarker() {
    mapboxController.markerController.removeMarkers(['selected-place']);
  }

  /// Adds a marker for the selected place
  void _addSelectedPlaceMarker() {
    if (state.selectedPlace == null) return;

    mapboxController.markerController.addMarkers([
      MarkerFeature(
        id: 'selected-place',
        properties: MarkerFeatureProperties(isSelected: false),
        geometry: MarkerFeatureGeometry(
          coordinates: [
            state.selectedPlace!.longitude,
            state.selectedPlace!.latitude,
          ],
        ),
      )
    ]);
  }

  /// Flies the map camera to the selected place
  Future<void> _flyToSelectedPlace() async {
    if (state.selectedPlace == null) return;

    await mapboxController.mapboxMap.flyTo(
      CameraOptions(
        center: Point(
          coordinates: Position(
            state.selectedPlace!.longitude,
            state.selectedPlace!.latitude,
          ),
        ),
        zoom: 14,
      ),
      MapAnimationOptions(duration: 50),
    );
  }
}

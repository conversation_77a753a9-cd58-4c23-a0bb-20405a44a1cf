import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Base controller for all iVent creation feature controllers
///
/// Provides common functionality and shared state access for all controllers
/// within the iVent creation feature. This includes authentication service access,
/// shared state management, and common error handling patterns.
///
/// All iVent creation controllers should extend this base class to ensure
/// consistent behavior and access to shared resources.
abstract class BaseIventCreateController extends BaseController {
  // Dependencies
  final IventCreateStateManager state;

  // Constructor
  BaseIventCreateController(AuthService authService, this.state) : super(authService);

  // Common methods

  /// Handles common error scenarios for iVent creation operations
  void handleError(dynamic error, [String? customMessage]) {
    final message = customMessage ?? 'Bir hata oluştu. Lütfen tekrar deneyin.';
    state.setError(message);
    print('IventCreate Error: $error');
  }

  /// Sets loading state through shared state manager
  void setLoading(bool loading) {
    state.setLoading(loading);
  }

  /// Clears any error state
  void clearError() {
    state.clearError();
  }

  /// Validates if required form data is present
  bool validateFormData() {
    if (!state.hasSelectedImage) {
      handleError(null, 'Lütfen bir görsel seçin.');
      return false;
    }

    if (!state.hasSelectedLocation) {
      handleError(null, 'Lütfen bir konum seçin.');
      return false;
    }

    return true;
  }
}

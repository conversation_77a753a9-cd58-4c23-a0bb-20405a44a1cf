import 'dart:typed_data';

import 'package:get/get.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';

/// State manager for iVent creation feature
///
/// Manages shared reactive state across all iVent creation controllers.
/// This includes form data, selected images, location data, and UI state
/// that needs to be accessible across multiple controllers within the feature.
class IventCreateStateManager extends GetxController {
  // Reactive state
  final _selectedImageUrl = Rxn<String>();
  final _selectedImageFile = Rxn<Uint8List>();
  final _selectedPlace = Rxn<IaLocationItem>();
  final _isLoading = false.obs;
  final _hasError = false.obs;
  final _errorMessage = ''.obs;
  final _currentStep = 0.obs;

  // Getters
  String? get selectedImageUrl => _selectedImageUrl.value;
  Uint8List? get selectedImageFile => _selectedImageFile.value;
  IaLocationItem? get selectedPlace => _selectedPlace.value;
  bool get isLoading => _isLoading.value;
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;
  int get currentStep => _currentStep.value;

  // Setters
  set selectedImageUrl(String? value) => _selectedImageUrl.value = value;
  set selectedImageFile(Uint8List? value) => _selectedImageFile.value = value;
  set selectedPlace(IaLocationItem? value) => _selectedPlace.value = value;
  set isLoading(bool value) => _isLoading.value = value;
  set hasError(bool value) => _hasError.value = value;
  set errorMessage(String value) => _errorMessage.value = value;
  set currentStep(int value) => _currentStep.value = value;

  // Methods

  /// Clears any error state
  void clearError() {
    hasError = false;
    errorMessage = '';
  }

  /// Sets error state with message
  void setError(String message) {
    hasError = true;
    errorMessage = message;
    isLoading = false;
  }

  /// Sets loading state
  void setLoading(bool loading) {
    isLoading = loading;
    if (loading) {
      clearError();
    }
  }

  /// Clears all form data
  void clearFormData() {
    selectedImageUrl = null;
    selectedImageFile = null;
    selectedPlace = null;
    currentStep = 0;
    clearError();
  }

  /// Checks if image is selected
  bool get hasSelectedImage => selectedImageUrl != null || selectedImageFile != null;

  /// Checks if location is selected
  bool get hasSelectedLocation => selectedPlace != null;
}

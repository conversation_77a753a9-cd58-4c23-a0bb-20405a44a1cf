import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/routes/ivent_create.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/base_ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';

class IventCreateImageController extends BaseIventCreateController {
  IventCreateImageController(AuthService authService, IventCreateStateManager state) : super(authService, state);

  final _suggestedImages = Rxn<GetSuggestedImagesReturn>();

  GetSuggestedImagesReturn? get suggestedImages => _suggestedImages.value;

  set suggestedImages(GetSuggestedImagesReturn? value) => _suggestedImages.value = value;

  Future<void> getSuggestedImagesPage() async {
    suggestedImages = await authService.iventsApi.getSuggestedImages('');
    Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_IMAGE_SELECTION);
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/features/ivent_create/widgets/navigation/ivent_name_top_bar_child.dart';

class IventCreateDescription extends StatelessWidget {
  final IventCreateController _controller = Get.find();
  final TextEditingController _textFieldController = TextEditingController();

  IventCreateFormController get _formController => _controller.formController;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return IaScaffold.noSearch(
        child: _buildTopBarChild(),
        showDivider: false,
        body: _buildDescriptionBody(),
      );
    });
  }

  Widget _buildTopBarChild() => IventNameTopBarChild(controller: _controller);

  Widget _buildDescriptionBody() {
    return Column(
      children: [
        const SizedBox(height: AppDimensions.padding20),
        Expanded(
          child: IaRoundedContainer(
            margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
            roundness: AppDimensions.radiusL,
            color: AppColors.lightGrey,
            child: TextField(
              onChanged: (value) => _formController.description = value,
              controller: _textFieldController,
              maxLines: null,
              style: AppTextStyles.size16Medium,
              decoration: InputDecoration(
                hintText: 'Etkinlik açıklamanızı bu alana yazabilirsiniz.',
                hintStyle: AppTextStyles.size16Regular,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(AppDimensions.padding20),
              ),
            ),
          ),
        ),
        IaFloatingActionButton(
          onPressed: () {
            _formController.description = _textFieldController.text;
            Get.back();
          },
          text: 'Açıklamayı Kaydet',
          isEnabled: _formController.description != null && _formController.description!.isNotEmpty,
        ),
      ],
    );
  }
}

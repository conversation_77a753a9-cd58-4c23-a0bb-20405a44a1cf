import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_basic_info_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_image_container.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/layout/navigation/ia_top_bar.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_bottom_panel.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_sliding_panel.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_create_buttons.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_detail_buttons.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_image_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_map_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_submission_controller.dart';
import 'package:ivent_app/features/ivent_create/widgets/panels/ivent_create_privacy_panel.dart';
import 'package:ivent_app/features/ivent_create/widgets/panels/ivent_create_register_type_panel.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class IventCreatePreview extends StatefulWidget {
  @override
  State<IventCreatePreview> createState() => _IventCreatePreviewState();
}

class _IventCreatePreviewState extends State<IventCreatePreview> {
  final IventCreateController _controller = Get.find();

  IventCreateMapController get _mapController => _controller.mapController;
  IventCreateFormController get _formController => _controller.formController;
  IventCreateSubmissionController get _submissionController => _controller.submissionController;
  IventCreateImageController get _imageController => _controller.imageController;

  List<Widget> panels = [
    const IventCreateRegisterTypePanel(),
    const IventCreatePrivacyPanel(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return IaSlidingPanel(
          panelController: _submissionController.panelController,
          minHeight: 0,
          maxHeight: 650,
          defaultPanelState: PanelState.CLOSED,
          onPanelClosed: () => _submissionController.isPanelVisible = false,
          onPanelOpened: () => _submissionController.isPanelVisible = true,
          panel: IaBottomPanel(
            showSlideIndicator: true,
            body: panels[_submissionController.selectedPanelIndex],
          ),
          body: SafeArea(
            child: Stack(
              children: [
                Column(
                  children: [
                    _buildTopBar(),
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.only(bottom: 100),
                        child: Column(
                          children: [
                            const SizedBox(height: AppDimensions.padding12),
                            _buildPreviewBody(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: IaFloatingActionButton(
                    text: 'Kayıt Türü Seç ve Yayınla',
                    onPressed: () => _submissionController.openPanel(0),
                    isEnabled: true,
                    isPrimary: false,
                    trailingIconPath: AppAssets.caretCircleRight,
                  ),
                ),
                Positioned.fill(
                  child: AnimatedOpacity(
                    opacity: _submissionController.isPanelVisible ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: AppDimensions.animationNormal),
                    child: IgnorePointer(
                      ignoring: !_submissionController.isPanelVisible,
                      child: IaRoundedContainer(
                        onTap: _submissionController.closePanel,
                        color: AppColors.black.withValues(alpha: 0.5),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildTopBar() {
    return IaTopBar.basic(
      showDivider: false,
      child: IaBasicInfoTile.withImageUrl(
        avatarUrl: _controller.sessionUser.sessionAvatarUrl,
        title: _controller.sessionUser.sessionFullname,
        subtitle: 'Düzenle',
      ),
      trailing: _buildPrivacyButton(),
    );
  }

  Widget _buildPrivacyButton() {
    var text;
    var iconPath;
    switch (_formController.selectedIventPrivacy) {
      case IventPrivacyEnum.friends:
        text = 'Arkadaşlar';
        iconPath = AppAssets.usersGroup;
        break;
      case IventPrivacyEnum.edu:
        text = 'Sadece Üniversite Öğrencileri';
        iconPath = AppAssets.usersGroup;
        break;
      case IventPrivacyEnum.selectedEdu:
        text = 'Seçili Üniversiteler';
        iconPath = AppAssets.usersGroup;
        break;
      case IventPrivacyEnum.public:
        text = 'Herkes';
        iconPath = AppAssets.globe;
        break;
    }

    return IventCreateButtons.privacyIvent(onTap: () {}, text: text, iconPath: iconPath);
  }

  Widget _buildPreviewBody() {
    return Column(
      children: [
        _buildThumbnail(),
        const SizedBox(height: AppDimensions.padding20),
        _buildDescription(),
        const SizedBox(height: AppDimensions.padding20),
        _buildIventTags(),
      ],
    );
  }

  Widget _buildThumbnail() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Stack(
        children: [
          _buildThumbnailImage(),
          _buildThumbnailOverlay(),
        ],
      ),
    );
  }

  Widget _buildThumbnailImage() {
    if (_imageController.state.selectedImageUrl == null) {
      return IaImageContainer.withChild(
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          child: Image.memory(_controller.state.selectedImageFile!, fit: BoxFit.cover),
        ),
      );
    }
    return IaImageContainer.withImageUrl(
      imageUrl: _imageController.state.selectedImageUrl,
      roundness: AppDimensions.radiusS,
    );
  }

  /// Builds the gradient overlay with ivent details and action buttons
  Widget _buildThumbnailOverlay() {
    return Positioned.fill(
      child: IaRoundedContainer(
        padding: const EdgeInsets.all(AppDimensions.padding16),
        roundness: AppDimensions.radiusS,
        gradient: AppColors.gradientBlackL,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildIventTitle(),
            const SizedBox(height: AppDimensions.padding8),
            _buildLocationName(),
            const SizedBox(height: AppDimensions.padding8),
            _buildDate(),
            const Spacer(),
            _buildActionButton(),
          ],
        ),
      ),
    );
  }

  /// Builds the ivent title row with arrow icon
  Widget _buildIventTitle() {
    return Text(
      _formController.iventName,
      style: AppTextStyles.size32BoldWhite,
      maxLines: 1,
      softWrap: false,
    );
  }

  /// Builds the location name text
  Widget _buildLocationName() {
    return Text(
      _mapController.state.selectedPlace?.name ?? '',
      style: AppTextStyles.size20BoldWhite,
      maxLines: 1,
      softWrap: false,
    );
  }

  /// Builds the date text
  Widget _buildDate() {
    final formattedDate = DateFormat('d MMMM yyyy, HH:mm').format(_formController.dates[0]);
    return Text(
      formattedDate,
      style: AppTextStyles.size20MediumWhite,
      maxLines: 1,
      softWrap: false,
    );
  }

  Widget _buildActionButton() {
    return IventDetailButtons.iventDetailTag(onTap: () {}, text: _formController.selectedCategory!.hobbyName);
  }

  Widget _buildDescription() {
    return IaRoundedContainer(
      onTap: _controller.goToDescriptionPage,
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      padding: const EdgeInsets.all(AppDimensions.padding20),
      roundness: AppDimensions.radiusS,
      color: AppColors.lightGrey,
      child: Row(
        children: [
          Text(
            _formController.description ?? 'Açıklama Ekle (Opsiyonel)',
            style: _formController.description != null
                ? AppTextStyles.size16Regular
                : AppTextStyles.size16RegularTextSecondary,
            maxLines: 1,
            softWrap: false,
          ),
          const Spacer(),
          const IaSvgIcon(iconPath: AppAssets.caretRightSM, iconColor: AppColors.mediumGrey),
        ],
      ),
    );
  }

  Widget _buildIventTags() {
    return IaRoundedContainer(
      onTap: _controller.goToTagsPage,
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      padding: const EdgeInsets.all(AppDimensions.padding20),
      roundness: AppDimensions.radiusS,
      color: AppColors.lightGrey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Etkinliğinizi ilgili kullanıcılara ulaştırmak için:',
                style: AppTextStyles.size16RegularTextSecondary,
                maxLines: 1,
                softWrap: false,
              ),
              const Spacer(),
              const IaSvgIcon(iconPath: AppAssets.caretRightSM, iconColor: AppColors.mediumGrey),
            ],
          ),
          if (_formController.selectedTags.isNotEmpty) const SizedBox(height: AppDimensions.padding20),
          Wrap(
            spacing: AppDimensions.padding8,
            runSpacing: AppDimensions.padding8,
            alignment: WrapAlignment.start,
            children: _formController.selectedTags
                .map((tag) => IventCreateButtons.iventCreateTag(
                      onTap: () => _formController.toggleTag(tag),
                      isSelected: _formController.selectedTagIds.contains(tag.hobbyId),
                      text: tag.hobbyName,
                    ))
                .toList(),
          ),
        ],
      ),
    );
  }
}

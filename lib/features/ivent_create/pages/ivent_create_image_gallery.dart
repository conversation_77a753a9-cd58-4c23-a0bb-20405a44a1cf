import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:photo_manager/photo_manager.dart' as photo_manager;
import 'package:photo_manager/photo_manager.dart';

class IventCreateImageGallery extends StatefulWidget {
  const IventCreateImageGallery({super.key});

  @override
  State<IventCreateImageGallery> createState() => _IventCreateImageGalleryState();
}

class _IventCreateImageGalleryState extends State<IventCreateImageGallery> {
  final IventCreateController _controller = Get.find();
  bool _isLoading = true;
  List<AssetEntity> _assets = [];

  @override
  void initState() {
    super.initState();
    _loadAssets();
  }

  Future<void> _loadAssets() async {
    final permission = await Permission.photos.request();
    if (permission.isDenied) return;

    final List<AssetEntity> assets = await PhotoManager.getAssetListPaged(
      page: 0,
      pageCount: 30,
      type: photo_manager.RequestType.image,
    );

    setState(() {
      _assets = assets;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      showDivider: false,
      bodyPadding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      body: DefaultTabController(
        length: 1,
        child: Column(
          children: [
            _buildTabButtons(),
            Expanded(child: _buildGallery()),
          ],
        ),
      ),
    );
  }

  Widget _buildTabButtons() {
    return Container(
      height: 30,
      child: TabBar(
        dividerHeight: 4,
        indicator: UnderlineTabIndicator(
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          borderSide: const BorderSide(width: 4, color: AppColors.primary),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: AppColors.lightGrey,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        tabs: [_buildTabButton('Camera Roll')],
      ),
    );
  }

  Widget _buildTabButton(String text) {
    return Tab(
      child: Text(
        text,
        style: AppTextStyles.size14Bold,
      ),
    );
  }

  Widget _buildGallery() {
    if (_isLoading) {
      return const IaLoadingIndicator();
    }
    return TabBarView(
      children: [_buildCameraRoll()],
    );
  }

  Widget _buildCameraRoll() {
    return GridView.builder(
      padding: const EdgeInsets.symmetric(vertical: 20),
      physics: const BouncingScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.6,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: _assets.length,
      itemBuilder: (context, index) {
        final element = _assets[index];
        return _buildImageGridItem(element);
      },
    );
  }

  void _handleImageTap(AssetEntity element) async {
    final file = await element.file;
    _controller.state.selectedImageFile = file?.readAsBytesSync();
    Get.back();
  }

  Widget _buildImageGridItem(AssetEntity element) {
    return GestureDetector(
      onTap: () => _handleImageTap(element),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: _buildImageItem(element),
      ),
    );
  }

  FutureBuilder<Uint8List?> _buildImageItem(AssetEntity element) {
    return FutureBuilder<Uint8List?>(
      future: element.thumbnailData,
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Image.memory(
            snapshot.data!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          );
          // return
        }
        return const SizedBox(
          child: Center(
            child: CircularProgressIndicator(strokeWidth: 2.0),
          ),
        );
      },
    );
  }
}

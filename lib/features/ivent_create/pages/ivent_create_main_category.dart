import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_create_buttons.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/features/ivent_create/constants/strings.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

/// Main category selection page for ivent creation.
///
/// This page allows users to select the primary category for their ivent
/// from a list of available hobby categories. The selection determines
/// the available sub-categories in the next step of the creation flow.
///
/// The page displays:
/// - A list of main categories with icons and names
/// - Visual feedback for the selected category
/// - Automatic navigation to sub-category selection upon selection
///
/// Categories are filtered to show only level 1 (main) categories from
/// the hobby entity system.
class IventCreateMainCategory extends StatefulWidget {
  const IventCreateMainCategory({super.key});

  @override
  State<IventCreateMainCategory> createState() => _IventCreateMainCategoryState();
}

/// Private state class for the main category selection page
class _IventCreateMainCategoryState extends State<IventCreateMainCategory> {
  /// The main ivent creation controller
  final IventCreateController _controller = Get.find<IventCreateController>();

  /// Index of the currently selected category (-1 means no selection)
  int _selectedIndex = -1;

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: IventCreateStrings.kategoriSec,
      showDivider: false,
      bodyPadding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      body: _buildCategoryList(),
    );
  }

  /// Builds the main category list
  Widget _buildCategoryList() {
    final categories = Hobby.filterHobbies(level: 1);

    return ListView.separated(
      padding: const EdgeInsets.only(
        top: AppDimensions.padding12,
        bottom: 100,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) => _buildCategoryItem(categories[index], index),
      separatorBuilder: IaListTile.separatorBuilder20,
    );
  }

  /// Builds an individual category list item
  Widget _buildCategoryItem(Hobby category, int index) {
    return IventCreateButtons.categoryBar(
      onTap: () => _handleCategorySelection(category, index),
      isSelected: _selectedIndex == index,
      text: category.hobbyName,
    );
  }

  /// Handles the selection of a category
  void _handleCategorySelection(Hobby category, int index) {
    setState(() {
      _selectedIndex = index;
    });

    // Update the form controller with the selected category
    _controller.formController.selectedCategory = category;

    // Navigate to sub-category selection
    _controller.goToSubCategoryPage();
  }
}

import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/base_vibes_controller.dart';
import 'package:ivent_app/features/vibes/models/vibe.dart';

/// Controller for managing a single vibe page
///
/// Handles loading and navigation of individual vibes with
/// next/previous functionality and video playback management.
class SinglePageVibeController extends BaseVibesController {
  final String vibeId;

  SinglePageVibeController(AuthService authService, this.vibeId) : super(authService);

  // Observable properties
  final _singleVibe = Rx<Vibe?>(null);

  // Getters
  Vibe? get singleVibe => _singleVibe.value;

  // Setters
  set singleVibe(Vibe? value) => _singleVibe.value = value;

  // Lifecycle methods
  @override
  Future<void> initController() async {
    super.initController();
    await _loadVibeById(vibeId);
    print('SingleVibeController has been initialized with user: ${sessionUser.sessionId}');
  }

  // Public methods

  /// Stops video playback if current vibe is a video
  Future<void> stopVideo() async {
    if (singleVibe == null || !singleVibe!.isVideo) return;
    singleVibe!.video!.pause();
  }

  /// Navigates to the next vibe if available
  Future<void> getNextVibe() async {
    if (singleVibe?.content.nextVibeId == null) return;
    await _loadVibeById(singleVibe!.content.nextVibeId!);
  }

  /// Navigates to the previous vibe if available
  Future<void> getPreviousVibe() async {
    if (singleVibe?.content.previousVibeId == null) return;
    await _loadVibeById(singleVibe!.content.previousVibeId!);
  }

  // Private methods

  /// Loads a vibe by its ID and updates the current vibe
  Future<void> _loadVibeById(String targetVibeId) async {
    try {
      // Pause current video if playing
      if (singleVibe?.isVideo == true) {
        singleVibe!.video!.pause();
      }

      final vibeData = await authService.vibesApi.getByVibeId(targetVibeId);
      if (vibeData == null) return;

      singleVibe = _createVibeFromData(vibeData);

      // Initialize and play video if it's a video vibe
      if (singleVibe!.isVideo) {
        await _loadVideo();
      }
    } catch (error) {
      handleVibesError(error, 'Vibe yüklenirken bir hata oluştu.');
    }
  }

  /// Creates a Vibe object from API response data
  Vibe _createVibeFromData(GetVibeByVibeIdReturn vibeData) {
    return Vibe(
      content: VibeItem(
        vibeId: vibeData.vibeId,
        vibeFolderId: vibeData.vibeFolderId,
        mediaUrl: vibeData.mediaUrl,
        mediaFormat: vibeData.mediaFormat,
        thumbnailUrl: vibeData.thumbnailUrl,
        caption: vibeData.caption,
        creatorId: vibeData.creatorId,
        creatorType: vibeData.creatorType,
        creatorUsername: vibeData.creatorUsername,
        creatorAvatarUrl: vibeData.creatorAvatarUrl,
        iventId: vibeData.iventId,
        iventName: vibeData.iventName,
        memberCount: vibeData.memberCount,
        likeCount: vibeData.likeCount,
        commentCount: vibeData.commentCount,
        nextVibeId: vibeData.nextVibeId,
        previousVibeId: vibeData.previousVibeId,
        vibeIndex: vibeData.vibeIndex,
        vibeCount: vibeData.vibeCount,
        createdAt: vibeData.createdAt,
      ),
      vibeFolderId: vibeData.vibeFolderId,
      videoManager: vibeData.mediaFormat == MediaFormatEnum.video ? videoManager : null,
    );
  }

  /// Loads and initializes video for the current vibe
  Future<void> _loadVideo() async {
    await singleVibe!.video?.initializeVideo(cacheManager);
    singleVibe!.video?.play();
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/controllers/privacy_settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/settings_list_tile.dart';

class PrivacySettingsPage extends StatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  State<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends State<PrivacySettingsPage> {
  late final PrivacySettingsController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(PrivacySettingsController());
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: 'Giz<PERSON>lik Ayarları',
      showBackButton: true,
      body: Obx(() => controller.isLoading.value
        ? const Center(child: CircularProgressIndicator())
        : RefreshIndicator(
            onRefresh: controller.refreshPrivacySettings,
            child: ListView(
              padding: const EdgeInsets.all(AppDimensions.padding20),
              children: [
                // Engellenen Kullanıcılar - TEK ÇALIŞAN API
                _buildSectionHeader('Engellenen Kullanıcılar'),
                _buildBlockedUsersSection(),
                
                const SizedBox(height: AppDimensions.padding20),
                
                // Bilgilendirme
                _buildInfoSection(),
              ],
            ),
          )
      ),
    );
  }

  Widget _buildBlockedUsersSection() {
    return Obx(() => SettingsListTile(
      icon: AppAssets.users,
      title: 'Engellenen Kişiler',
      subtitle: controller.blockedUsers.isEmpty 
        ? 'Henüz kimseyi engellemediniz'
        : '${controller.blockedUsersCount} kişi engellendi',
      onTap: () => Get.toNamed('/settings/blocked-users'),
      iconColor: AppColors.error,
      trailing: controller.isLoadingBlocked.value 
        ? const SizedBox(
            width: 20, 
            height: 20, 
            child: CircularProgressIndicator(strokeWidth: 2)
          )
        : null,
    ));
  }

  Widget _buildInfoSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.info.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: AppColors.info,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.padding8),
              Text(
                'Gizlilik Bilgisi',
                style: AppTextStyles.size14BoldPrimary,
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.padding8),
          Text(
            'Şu anda sadece engellenen kullanıcılar listesi backend ile entegre çalışmaktadır. '
            'Diğer gizlilik ayarları backend API\'leri hazır olduğunda eklenecektir.',
            style: AppTextStyles.size12Regular,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppDimensions.padding12,
        top: AppDimensions.padding16,
      ),
      child: Text(
        title,
        style: AppTextStyles.size14BoldPrimary,
      ),
    );
  }
} 
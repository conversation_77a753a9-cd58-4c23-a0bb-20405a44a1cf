import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/controllers/security_settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/settings_list_tile.dart';

class SecuritySettingsPage extends StatefulWidget {
  const SecuritySettingsPage({super.key});

  @override
  State<SecuritySettingsPage> createState() => _SecuritySettingsPageState();
}

class _SecuritySettingsPageState extends State<SecuritySettingsPage> {
  late final SecuritySettingsController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(SecuritySettingsController());
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: '<PERSON>ü<PERSON><PERSON>',
      showBackButton: true,
      body: Obx(() => controller.isLoading.value
        ? const Center(child: CircularProgressIndicator())
        : RefreshIndicator(
            onRefresh: controller.refreshSettings,
            child: ListView(
              padding: const EdgeInsets.all(AppDimensions.padding16),
              children: [
                // Hesap Bilgileri Update
                _buildSectionHeader('Hesap Bilgileri'),
                SettingsListTile(
                  icon: AppAssets.mail,
                  title: 'E-posta Adresi Güncelle',
                  subtitle: 'Yeni e-posta adresi ekle',
                  onTap: controller.showEmailUpdateDialog,
                ),
                const SizedBox(height: 8),
                SettingsListTile(
                  icon: AppAssets.phone,
                  title: 'Telefon Numarası Güncelle',
                  subtitle: 'Yeni telefon numarası ekle',
                  onTap: controller.showPhoneUpdateDialog,
                ),
                const SizedBox(height: 8),
                SettingsListTile(
                  icon: AppAssets.mail,
                  title: 'E-posta Doğrulama Kodu Gönder',
                  subtitle: 'Hesabınızı doğrulamak için kod gönderin',
                  onTap: controller.sendVerificationEmail,
                ),
                
                const SizedBox(height: AppDimensions.padding20),
                
                // Eğitim Durumu
                _buildSectionHeader('Eğitim Durumu'),
                Obx(() => SettingsListTile(
                  icon: AppAssets.userSquare,
                  title: 'Eğitim Durumu',
                  subtitle: controller.getEducationStatusText(),
                  titleColor: controller.getEducationStatusColor(),
                  trailing: DropdownButton<String>(
                    value: controller.educationStatus.value,
                    underline: const SizedBox(),
                    items: const [
                      DropdownMenuItem(value: 'unverified', child: Text('Doğrulanmadı')),
                      DropdownMenuItem(value: 'student', child: Text('Öğrenci')),
                      DropdownMenuItem(value: 'grad', child: Text('Mezun')),
                      DropdownMenuItem(value: 'verified', child: Text('Doğrulandı')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        controller.updateEducationStatus(value);
                      }
                    },
                  ),
                )),
                
                const SizedBox(height: AppDimensions.padding20),
                
                // Bildirim Ayarları
                _buildSectionHeader('Bildirim Ayarları'),
                Obx(() => SettingsListTile(
                  icon: AppAssets.bellNotification,
                  title: 'Push Bildirimleri',
                  subtitle: 'Uygulama bildirimleri',
                  trailing: Switch(
                    value: controller.pushNotifications.value,
                    onChanged: (value) {
                      controller.pushNotifications.value = value;
                      controller.updateNotificationSettings();
                    },
                    activeColor: AppColors.primary,
                  ),
                )),
                const SizedBox(height: 8),
                Obx(() => SettingsListTile(
                  icon: AppAssets.mail,
                  title: 'E-posta Bildirimleri',
                  subtitle: 'E-posta ile bildirim al',
                  trailing: Switch(
                    value: controller.emailNotifications.value,
                    onChanged: (value) {
                      controller.emailNotifications.value = value;
                      controller.updateNotificationSettings();
                    },
                    activeColor: AppColors.primary,
                  ),
                )),
                const SizedBox(height: 8),
                Obx(() => SettingsListTile(
                  icon: AppAssets.phone,
                  title: 'SMS Bildirimleri',
                  subtitle: 'SMS ile bildirim al',
                  trailing: Switch(
                    value: controller.smsNotifications.value,
                    onChanged: (value) {
                      controller.smsNotifications.value = value;
                      controller.updateNotificationSettings();
                    },
                    activeColor: AppColors.primary,
                  ),
                )),
                const SizedBox(height: 8),
                Obx(() => SettingsListTile(
                  icon: AppAssets.alarm,
                  title: 'Etkinlik Hatırlatıcıları',
                  subtitle: 'Etkinlik öncesi hatırlatma',
                  trailing: Switch(
                    value: controller.eventReminders.value,
                    onChanged: (value) {
                      controller.eventReminders.value = value;
                      controller.updateNotificationSettings();
                    },
                    activeColor: AppColors.primary,
                  ),
                )),
                const SizedBox(height: 8),
                Obx(() => SettingsListTile(
                  icon: AppAssets.usersGroup,
                  title: 'Sosyal Bildirimler',
                  subtitle: 'Arkadaş istekleri, takip bildirimleri',
                  trailing: Switch(
                    value: controller.socialNotifications.value,
                    onChanged: (value) {
                      controller.socialNotifications.value = value;
                      controller.updateNotificationSettings();
                    },
                    activeColor: AppColors.primary,
                  ),
                )),
                const SizedBox(height: 8),
                Obx(() => SettingsListTile(
                  icon: AppAssets.mailOpen,
                  title: 'Pazarlama E-postaları',
                  subtitle: 'Kampanya ve duyuru e-postaları',
                  trailing: Switch(
                    value: controller.marketingEmails.value,
                    onChanged: (value) {
                      controller.marketingEmails.value = value;
                      controller.updateNotificationSettings();
                    },
                    activeColor: AppColors.primary,
                  ),
                )),
                
                const SizedBox(height: AppDimensions.padding20),
                
                // Hesap Güvenliği
                _buildSectionHeader('Hesap Güvenliği'),
                Obx(() => SettingsListTile(
                  icon: AppAssets.shieldCheck,
                  title: 'İki Faktörlü Doğrulama',
                  subtitle: 'Hesabınıza ekstra güvenlik katmanı ekleyin',
                  trailing: Switch(
                    value: controller.twoFactorAuth.value,
                    onChanged: (value) => controller.updateSecuritySetting('twoFactorAuth', value),
                    activeColor: AppColors.primary,
                  ),
                )),
                const SizedBox(height: 8),
                Obx(() => SettingsListTile(
                  icon: AppAssets.info,
                  title: 'Giriş Uyarıları',
                  subtitle: 'Hesabınıza yeni bir cihazdan giriş yapıldığında bildirim alın',
                  trailing: Switch(
                    value: controller.loginAlerts.value,
                    onChanged: (value) => controller.updateSecuritySetting('loginAlerts', value),
                    activeColor: AppColors.primary,
                  ),
                )),
                
                const SizedBox(height: AppDimensions.padding20),
                
                // Hesap Silme
                SettingsListTile(
                  icon: AppAssets.circleWarning,
                  title: 'Hesabı Sil',
                  subtitle: 'Hesabınızı kalıcı olarak silin',
                  titleColor: Colors.red,
                  onTap: () => _showDeleteAccountDialog(context),
                ),
              ],
            ),
          )
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppDimensions.padding12,
        top: AppDimensions.padding16,
      ),
      child: Text(
        title,
        style: AppTextStyles.size14BoldPrimary,
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('Hesabı Sil'),
        content: const Text(
          'Hesabınızı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('İptal'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              // TODO: Implement account deletion
              Get.snackbar(
                'Bilgi',
                'Hesap silme özelliği yakında aktif olacak.',
                backgroundColor: Colors.orange,
                colorText: Colors.white,
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Sil', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
} 
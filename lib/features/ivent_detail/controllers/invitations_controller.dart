import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/routes/auth.dart';
import 'package:ivent_app/routes/ivent_detail.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/dialogs/ia_alert_dialog.dart';
import 'package:ivent_app/features/ivent_detail/controllers/base_ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';

/// Controller for managing iVent invitations and user selection
///
/// Handles invitation functionality including loading invitable users and groups,
/// managing selection state, and processing invitation requests. Supports both
/// joining iVents with friends and inviting friends to existing iVents.
class InvitationsController extends BaseIventDetailsController {
  // Reactive state
  final _invitableUsers = Rxn<SearchInvitableUsersByIventIdReturn>();
  final _invitableGroups = Rxn<SearchInvitableUsersByIventIdReturn>();
  final _collabs = Rxn<SearchCollabsReturn>();
  final _selectedInvitableGroups = RxList<GroupListItem>([]);
  final _selectedInvitableUsers = RxList<UserListItem>([]);

  // Constructor
  InvitationsController(
    AuthService authService,
    IventDetailStateManager state,
    String iventId,
  ) : super(authService, state, iventId);

  // Getters
  SearchInvitableUsersByIventIdReturn? get invitableUsers => _invitableUsers.value;
  SearchInvitableUsersByIventIdReturn? get invitableGroups => _invitableGroups.value;
  SearchCollabsReturn? get collabs => _collabs.value;
  List<GroupListItem> get selectedInvitableGroups => _selectedInvitableGroups;
  List<UserListItem> get selectedInvitableUsers => _selectedInvitableUsers;

  // Setters
  set invitableUsers(SearchInvitableUsersByIventIdReturn? value) => _invitableUsers.value = value;
  set invitableGroups(SearchInvitableUsersByIventIdReturn? value) => _invitableGroups.value = value;
  set collabs(SearchCollabsReturn? value) => _collabs.value = value;
  set selectedInvitableGroups(List<GroupListItem> value) => _selectedInvitableGroups.assignAll(value);
  set selectedInvitableUsers(List<UserListItem> value) => _selectedInvitableUsers.assignAll(value);

  // Computed properties

  /// Total count of selected friends (users + group members)
  int get selectedFriendCount {
    final memberCountFromGroups = selectedInvitableGroups.fold<int>(
      0,
      (previousValue, element) => previousValue + element.memberCount,
    );
    return memberCountFromGroups + selectedInvitableUsers.length;
  }

  /// Combined list of selected friend names
  List<String> get selectedFriendNames {
    final memberNamesFromGroups = selectedInvitableGroups.fold<List<String>>(
      [],
      (previousValue, element) {
        previousValue.addAll(element.memberFirstnames);
        return previousValue;
      },
    );
    final usernamesFromUsers = selectedInvitableUsers.map((element) => element.username).toList();
    return usernamesFromUsers + memberNamesFromGroups;
  }

  /// List of selected group IDs
  List<String> get selectedInvitableGroupIds {
    return selectedInvitableGroups.map((element) => element.groupId).toList();
  }

  /// List of selected user IDs
  List<String> get selectedInvitableUserIds {
    return selectedInvitableUsers.map((element) => element.userId).toList();
  }

  /// List of selected user first names
  List<String> get selectedInvitableUserFirstNames {
    return selectedInvitableUsers.map((element) => element.username).toList();
  }

  /// Generates invitation summary text for display
  String get getInvitedSummmaryText {
    const endingText = 'katılım bilgilerini içeren bir bildirim gönderdik.';

    if (selectedFriendCount == 0) {
      return '';
    } else if (selectedFriendCount == 1) {
      return "${selectedFriendNames[0]}'a $endingText";
    } else if (selectedFriendCount == 2) {
      return "${selectedFriendNames[0]} ve ${selectedFriendNames[1]}'a $endingText";
    } else {
      return '${selectedFriendNames[0]}, ${selectedFriendNames[1]} ve ${selectedFriendCount - 2} diğer arkadaşına $endingText';
    }
  }

  // Public methods

  /// Navigates to initial invitation page and loads invitable users/groups
  Future<void> getInitiallyInvitableUsersPage() async {
    try {
      Get.toNamed(IventDetayRoutes.IVENT_DETAY_KIMLERLE_KATILIYORSUN, arguments: iventId);
      await _loadInvitableUsersAndGroups();
    } catch (e) {
      handleError(e, 'Davet sayfası yüklenirken bir hata oluştu.');
    }
  }

  /// Navigates to invite more users page and loads invitable users/groups
  Future<void> getInviteMoreUsersPage() async {
    try {
      Get.toNamed(IventDetayRoutes.IVENT_DETAY_DAHA_FAZLA_KISI_CAGIR, arguments: iventId);
      await _loadInvitableUsersAndGroups();
    } catch (e) {
      handleError(e, 'Davet sayfası yüklenirken bir hata oluştu.');
    }
  }

  /// Toggles selection state of an invitable group
  void toggleInvitableGroup(GroupListItem group) {
    if (selectedInvitableGroupIds.contains(group.groupId)) {
      selectedInvitableGroups = selectedInvitableGroups.where((element) => element.groupId != group.groupId).toList();
    } else {
      selectedInvitableGroups.add(group);
    }
  }

  /// Toggles selection state of an invitable user
  void toggleInvitableUser(UserListItem user) {
    if (selectedInvitableUserIds.contains(user.userId)) {
      selectedInvitableUsers = selectedInvitableUsers.where((element) => element.userId != user.userId).toList();
    } else {
      selectedInvitableUsers.add(user);
    }
  }

  /// Joins iVent with selected friends
  Future<void> joinIvent() async {
    if (selectedFriendCount == 0) return;

    try {
      setLoading(true);

      await authService.squadMembershipsApi.joinIventAndCreateSquadByIventId(
        iventId,
        JoinIventAndCreateSquadByIventIdDto(
          groupIds: selectedInvitableGroupIds,
          userIds: selectedInvitableUserIds,
        ),
      );

      _clearSelections();
      Get.back();
      setLoading(false);
    } catch (e) {
      handleError(e, 'iVent\'e katılırken bir hata oluştu.');
    }
  }

  /// Joins iVent with friends and shows confirmation dialog
  Future<void> joinIventAndInviteFriends(BuildContext context) async {
    if (selectedFriendCount == 0) return;

    try {
      setLoading(true);
      final userFirstNames = selectedInvitableUserFirstNames;
      final userCount = selectedInvitableUsers.length;

      await authService.squadMembershipsApi.joinIventAndCreateSquadByIventId(
        iventId,
        JoinIventAndCreateSquadByIventIdDto(
          groupIds: selectedInvitableGroupIds,
          userIds: selectedInvitableUserIds,
        ),
      );

      _clearSelections();
      Get.back();
      setLoading(false);

      // Show success dialog
      Future.microtask(
        () => showDialog(
          context: context,
          builder: (context) => IaAlertDialog.iventJoin(
            userFirstNames: userFirstNames,
            userCount: userCount,
            onHomePage: () => Get.offAllNamed(AuthRoutes.APP_NAVIGATION),
            onDone: () => Get.back(),
          ),
        ),
      );
    } catch (e) {
      handleError(e, 'iVent\'e katılırken bir hata oluştu.');
    }
  }

  /// Invites friends to iVent and shows confirmation dialog
  Future<void> inviteFriends(BuildContext context) async {
    if (selectedFriendCount == 0) return;

    try {
      setLoading(true);
      final selectedFriendFirstNames = selectedInvitableUserFirstNames;

      await authService.squadMembershipsApi.inviteFriendsByIventId(
        iventId,
        InviteFriendsByIventIdDto(
          groupIds: selectedInvitableGroupIds,
          userIds: selectedInvitableUserIds,
        ),
      );

      _clearSelections();
      Get.close(2);
      setLoading(false);

      // Show success dialog
      Future.microtask(
        () => showDialog(
          context: context,
          builder: (context) => IaAlertDialog.iventInvite(
            userFirstNames: selectedFriendFirstNames,
            userCount: selectedFriendFirstNames.length,
            onHomePage: () => Get.offAllNamed(AuthRoutes.APP_NAVIGATION),
            onDone: () => Get.back(),
          ),
        ),
      );
    } catch (e) {
      handleError(e, 'Arkadaşlar davet edilirken bir hata oluştu.');
    }
  }

  // Private methods

  /// Loads invitable users and groups from API
  Future<void> _loadInvitableUsersAndGroups() async {
    setLoading(true);

    final futures = await Future.wait([
      authService.squadMembershipsApi.searchInvitableUsersByIventId(
        iventId,
        FriendListingTypeEnum.group,
      ),
      authService.squadMembershipsApi.searchInvitableUsersByIventId(
        iventId,
        FriendListingTypeEnum.user,
      ),
    ]);

    invitableGroups = futures[0];
    invitableUsers = futures[1];
    setLoading(false);
  }

  /// Clears all selections
  void _clearSelections() {
    selectedInvitableGroups.clear();
    selectedInvitableUsers.clear();
  }
}

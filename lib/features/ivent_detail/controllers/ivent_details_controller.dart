import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/base_ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/collabs_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/invitations_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_info_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/participants_controller.dart';

/// Main controller for iVent detail feature
///
/// Orchestrates all child controllers and manages navigation between
/// different sections of the iVent detail feature. This controller
/// serves as the main entry point and coordinator for all iVent detail
/// functionality.
class IventDetailsController extends BaseIventDetailsController {
  // Child controllers
  late final IventInfoController iventInfoController;
  late final ParticipantsController participantsController;
  late final InvitationsController invitationsController;
  late final CollabsController collabsController;

  // Constructor
  IventDetailsController(
    AuthService authService,
    IventDetailStateManager state,
    String iventId,
  ) : super(authService, state, iventId);

  // Lifecycle methods

  @override
  void initController() async {
    super.initController();
    await _initializeChildControllers();
  }

  @override
  void closeController() {
    _disposeChildControllers();
    super.closeController();
  }

  // Navigation methods

  /// Navigates to initially invitable users page
  Future<void> goToInitiallyInvitableUsersPage() async {
    try {
      await invitationsController.getInitiallyInvitableUsersPage();
    } catch (e) {
      handleError(e, 'Davet sayfası açılırken bir hata oluştu.');
    }
  }

  /// Navigates to invite more users page
  Future<void> goToInviteMoreUsersPage() async {
    try {
      await invitationsController.getInviteMoreUsersPage();
    } catch (e) {
      handleError(e, 'Davet sayfası açılırken bir hata oluştu.');
    }
  }

  /// Navigates to participants page
  Future<void> goToParticipantsPage(
    IventViewTypeEnum viewType,
    int participantCount,
  ) async {
    try {
      await participantsController.getParticipantsPage(viewType, participantCount);
    } catch (e) {
      handleError(e, 'Katılımcılar sayfası açılırken bir hata oluştu.');
    }
  }

  /// Navigates to collaborators page
  Future<void> goToCollabsPage() async {
    try {
      await collabsController.getCollabsPage();
    } catch (e) {
      handleError(e, 'Paydaşlar sayfası açılırken bir hata oluştu.');
    }
  }

  // Private methods

  /// Initializes all child controllers
  Future<void> _initializeChildControllers() async {
    iventInfoController = Get.put(
      IventInfoController(authService, state, iventId),
      tag: iventId,
    );
    participantsController = Get.put(
      ParticipantsController(authService, state, iventId),
      tag: iventId,
    );
    invitationsController = Get.put(
      InvitationsController(authService, state, iventId),
      tag: iventId,
    );
    collabsController = Get.put(
      CollabsController(authService, state, iventId),
      tag: iventId,
    );
  }

  /// Disposes all child controllers
  void _disposeChildControllers() {
    Get.delete<IventInfoController>(tag: iventId);
    Get.delete<ParticipantsController>(tag: iventId);
    Get.delete<InvitationsController>(tag: iventId);
    Get.delete<CollabsController>(tag: iventId);
  }
}

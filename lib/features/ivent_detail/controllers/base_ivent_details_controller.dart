import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

/// Base controller for all iVent detail feature controllers
///
/// Provides common functionality and shared state access for all controllers
/// within the iVent detail feature. This includes authentication service access,
/// shared state management, and common error handling patterns.
///
/// All iVent detail controllers should extend this base class to ensure
/// consistent behavior and access to shared resources.
abstract class BaseIventDetailsController extends BaseController {
  // Dependencies
  final IventDetailStateManager state;
  final String iventId;

  // Constructor
  BaseIventDetailsController(
    AuthService authService,
    this.state,
    this.iventId,
  ) : super(authService);

  // Common methods

  /// Handles common error scenarios for iVent detail operations
  void handleError(dynamic error, [String? customMessage]) {
    final message = customMessage ?? 'Bir hata oluştu. Lütfen tekrar deneyin.';
    state.setError(message);
    print('IventDetail Error: $error');
  }

  /// Sets loading state through shared state manager
  void setLoading(bool loading) {
    state.setLoading(loading);
  }

  /// Clears any error state
  void clearError() {
    state.clearError();
  }
}

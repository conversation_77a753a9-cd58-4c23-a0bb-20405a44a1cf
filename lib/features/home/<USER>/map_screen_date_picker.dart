import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/buttons/home_buttons.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/calendar.dart';

/// Date picker widget specifically for the map screen
///
/// Provides a compact date filter button that expands to show
/// a full calendar when tapped. Allows selecting date ranges
/// for filtering map ivents.
class MapScreenDatePicker extends StatefulWidget {
  const MapScreenDatePicker({
    super.key,
    required HomeController controller,
  }) : _controller = controller;

  final HomeController _controller;

  @override
  State<MapScreenDatePicker> createState() => _MapScreenDatePickerState();
}

class _MapScreenDatePickerState extends State<MapScreenDatePicker> {
  bool _isCalendarVisible = false;
  List<DateTime?> _selectedDates = [];

  void _toggleCalendarVisibility() => setState(() => _isCalendarVisible = !_isCalendarVisible);

  void _applySelectedDates() {
    _toggleCalendarVisibility();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        HomeButtons.mapDateFilter(
          onTap: _toggleCalendarVisibility,
          text: widget._controller.homeMapController.todayAsString,
        ),
        const SizedBox(height: AppDimensions.padding20),
        if (_isCalendarVisible)
          Calendar(
            selectedDates: _selectedDates,
            onDateRangeSelected: (dates) => setState(() => _selectedDates = dates),
            onDateRangeAccepted: _applySelectedDates,
          ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ia_ivent_box.dart';

/// A widget for displaying ivent information on the map
///
/// Shows a compact banner with ivent details that appears
/// when an ivent marker is selected on the map.
class MapIventBox extends StatelessWidget {
  final IventCardItem iventBanner;

  const MapIventBox({
    super.key,
    required this.iventBanner,
  });

  @override
  Widget build(BuildContext context) {
    return _buildIventBox(context);
  }

  /// Builds the main ivent box container with ivent details
  Widget _buildIventBox(BuildContext context) {
    return IaIventBox(
      width: 190 * 0.87, // TODO: Move to constants
      height: 190,
      color: AppColors.white,
      iventId: iventBanner.iventId,
      iventName: iventBanner.iventName,
      thumbnailUrl: iventBanner.thumbnailUrl,
      locationName: iventBanner.locationName,
      creatorId: iventBanner.creatorId,
      creatorName: iventBanner.creatorUsername,
      creatorImageUrl: iventBanner.creatorImageUrl,
      isFavorited: iventBanner.isFavorited,
      onFavorite: () {},
    );
  }
}
